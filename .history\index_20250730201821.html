<!DOCTYPE html>
<html lang="th">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ScareScore - RSU Dent</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- PapaParse Library for CSV parsing -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.2/papaparse.min.js"></script>
  <style type="text/tailwindcss">
    /* ลบ .card และ .container-main ออก เพราะใช้ container/mx-auto/w-full/lg:max-w-[75%] ตามตัวอย่าง */
    .spinner {
      @apply inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em];
    }
    .spinner-sm {
      @apply h-3 w-3;
    }
    .spinner-lg {
      @apply h-8 w-8;
    }
    .spinner-xl {
      @apply h-12 w-12 border-4;
    }
    .badge {
      @apply inline-flex items-center rounded-lg px-2.5 py-1 text-xs font-medium ring-1 ring-inset;
    }
    /* Stat-specific badges: make them smaller */
    .badge.stat-badge {
      @apply px-1 py-0.5 text-[10px];
    }
    .badge-morning {
      @apply bg-blue-50 text-blue-700 ring-blue-600/20;
    }
    .badge-afternoon {
      @apply bg-amber-50 text-amber-700 ring-amber-600/20;
    }
    .badge-evening {
      @apply bg-purple-50 text-purple-700 ring-purple-600/20;
    }
    .badge-work {
      @apply bg-gray-100 text-gray-700 ring-gray-600/20;
    }
    .badge-blue {
      @apply bg-blue-50 text-blue-700 ring-blue-600/20;
    }
    .badge-green {
      @apply bg-green-50 text-green-700 ring-green-600/20;
    }
    .badge-yellow {
      @apply bg-yellow-50 text-yellow-700 ring-yellow-600/20;
    }
    .badge-red {
      @apply bg-red-50 text-red-700 ring-red-600/20;
    }
    .badge-purple {
      @apply bg-purple-50 text-purple-700 ring-purple-600/20;
    }
    /* Tab styles */
    .tab-button {
      @apply transition-colors duration-200;
    }
    .tab-content {
      @apply transition-all duration-200;
    }

    /* Success Modal Styles */
    .modal-backdrop {
      @apply fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity;
    }

    .success-modal {
      @apply fixed inset-0 flex items-center justify-center z-50 p-4;
    }

    .success-modal-content {
      @apply bg-white rounded-xl shadow-2xl max-w-md w-full transform transition-all duration-300 ease-in-out;
    }

    .success-modal.hidden .success-modal-content {
      transform: scale(0.95);
      opacity: 0;
    }

    .success-modal.visible .success-modal-content {
      transform: scale(1);
      opacity: 1;
    }

    /* Highlight Animation */
    @keyframes highlight-pulse {
      0%, 100% {
        border-color: rgb(59 130 246);
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
      }
      50% {
        border-color: rgb(37 99 235);
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
      }
    }

    .highlight-new {
      animation: highlight-pulse 2s ease-in-out 3;
      border-color: rgb(59 130 246) !important;
    }

    /* Custom Toggle Switch */
    .toggle-switch {
      position: relative;
      width: 3rem;
      height: 1.5rem;
      background-color: rgb(156 163 175);
      border-radius: 9999px;
      transition: background-color 0.2s;
      cursor: pointer;
    }

    .toggle-switch.active {
      background-color: rgb(59 130 246);
    }

    .toggle-slider {
      position: absolute;
      top: 2px;
      left: 2px;
      width: 1.25rem;
      height: 1.25rem;
      background-color: white;
      border-radius: 50%;
      transition: transform 0.2s;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .toggle-switch.active .toggle-slider {
      transform: translateX(1.5rem);
    }
  </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
  <header class="border-b bg-white">
    <div class="container mx-auto px-4 py-4">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
        <div class="flex justify-between items-center md:flex-1">
          <div class="flex flex-col">
            <h1 class="text-2xl font-bold text-gray-900">
              <i class="fa-regular fa-face-grin-beam-sweat mr-2"></i>Scare<span class="font-light">Score</span>
            </h1>
            <p class="text-xs md:text-sm text-gray-500 mt-1">ระบบประกาศคะแนนสอบ Dent RSU</p>
          </div>
          <div id="user-info" class="text-sm text-gray-700 text-right flex items-center justify-end ml-4 md:hidden"></div>
        </div>
        <div id="user-info-desktop" class="text-sm text-gray-700 text-right flex items-center justify-end hidden md:flex"></div>
      </div>
    </div>
  </header>
  <main class="px-4 py-8">
    <div class="container mx-auto w-full lg:max-w-[75%] space-y-4">
      <div id="main-card" class="bg-white rounded-lg border shadow-sm p-4 mb-4">
        <div id="status" class="mb-4 text-center"></div>
        <div id="content"></div>
        <div id="student-courses"></div>
        <!-- คะแนนแต่ละวิชาจะแสดง inline ใต้การ์ด -->
      </div>
    </div>
  </main>
  <footer class="text-center text-xs text-gray-400 py-2 border-t">&copy; 2025 <EMAIL></footer>
  <script>
    function renderStudentCourses() {
      document.getElementById('student-courses').innerHTML = `
        <div class="flex flex-col items-center justify-center py-8">
          <div class="spinner spinner-lg text-blue-500"></div>
        </div>
      `;
      google.script.run.withSuccessHandler(async function(courses) {
        console.log('courses:', courses);
        // ตรวจสอบ error หรือข้อมูลไม่ถูกต้อง
        if (!courses) {
          document.getElementById('student-courses').innerHTML = `
            <div class="text-center py-8">
              <i class="fas fa-exclamation-triangle text-yellow-400 text-4xl mb-4"></i>
              <p class="text-gray-500">เกิดข้อผิดพลาดในการโหลดข้อมูล</p>
              <button onclick="renderStudentCourses()" class="mt-2 text-blue-500 hover:text-blue-600 text-sm">
                <i class="fas fa-redo mr-1"></i>ลองใหม่
              </button>
            </div>
          `;
          return;
        }

        if (!Array.isArray(courses)) {
          console.error('Invalid courses data:', courses);
          document.getElementById('student-courses').innerHTML = `
            <div class="text-center py-8">
              <i class="fas fa-exclamation-triangle text-yellow-400 text-4xl mb-4"></i>
              <p class="text-gray-500">ข้อมูลไม่ถูกต้อง</p>
              <button onclick="renderStudentCourses()" class="mt-2 text-blue-500 hover:text-blue-600 text-sm">
                <i class="fas fa-redo mr-1"></i>ลองใหม่
              </button>
            </div>
          `;
          return;
        }

        if (courses.length === 0) {
          document.getElementById('student-courses').innerHTML = `
            <div class="text-center py-8">
              <i class="fas fa-inbox text-gray-400 text-4xl mb-4"></i>
              <p class="text-gray-500">ไม่พบรายวิชาที่ประกาศคะแนน</p>
              <p class="text-xs text-gray-400 mt-2">รายวิชาจะแสดงเมื่ออาจารย์ประกาศคะแนนแล้ว</p>
            </div>
          `;
          return;
        }
        // เรียงลำดับ courses ตามวันที่ประกาศล่าสุดไว้ข้างบน
        courses.sort((a, b) => new Date(b.announceDate) - new Date(a.announceDate));

        let html = `
          <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
            <p class="text-xs text-yellow-700">
              <i class="fas fa-info-circle mr-2"></i>
              คะแนนจะแสดงผล 30 วัน หลังจากนั้นจะถูกลบโดยอัตโนมัติ
            </p>
          </div>
          <div class="grid gap-4 mt-4">
        `;
        window.selectedCourseIndexes = Array.isArray(window.selectedCourseIndexes) ? window.selectedCourseIndexes : [];
        // preload scores for all courses
        window.courseScoresCache = window.courseScoresCache || {};
        const preloadPromises = courses.map((c, i) => {
          if (window.courseScoresCache[c.scoreSheetName]) return Promise.resolve();
          return new Promise((resolve) => {
            google.script.run.withSuccessHandler(function(result) {
              window.courseScoresCache[c.scoreSheetName] = result;
              resolve();
            }).doGetStudentCourseScores(c.scoreSheetName);
          });
        });
        // preload all
        await Promise.all(preloadPromises);
        for (let i = 0; i < courses.length; i++) {
          const c = courses[i];

          // ตรวจสอบสถานะ active/inactive - ถ้า inactive ไม่แสดงรายวิชานี้
          const isActive = c.isActive !== false; // Default to true if not specified
          if (!isActive) {
            console.log('Skipping inactive course:', c.code);
            continue; // ข้ามรายวิชาที่ inactive
          }

          // กำหนด icon และ badge class สำหรับประเภทวิชา (examType)
          let examTypeIcon = 'fa-book';
          let badgeClass = 'badge-work';
          if (c.examType?.toLowerCase().includes('mid')) {
            examTypeIcon = 'fa-pen-to-square'; badgeClass = 'badge-blue';
          } else if (c.examType?.toLowerCase().includes('final')) {
            examTypeIcon = 'fa-graduation-cap'; badgeClass = 'badge-green';
          } else if (c.examType?.toLowerCase().includes('quiz')) {
            examTypeIcon = 'fa-question'; badgeClass = 'badge-yellow';
          } else if (c.examType?.toLowerCase().includes('practical')) {
            examTypeIcon = 'fa-flask'; badgeClass = 'badge-red';
          }
          const isOpen = window.selectedCourseIndexes && window.selectedCourseIndexes.includes(i);

          console.log('Showing active course:', c.code, 'isActive:', c.isActive);

          html += `
            <div class="rounded-xl shadow bg-white mb-3 border border-gray-100">
              <button class="w-full flex flex-row justify-between items-center px-4 py-1.5 focus:outline-none" onclick="toggleCourseScores(${i}, '${c.scoreSheetName}','${c.code}','${c.name}','${c.announceDate}')">
                <div class="flex flex-col justify-center text-left h-full gap-0.5">
                  <div class="font-bold text-blue-700 flex items-center gap-2">
                    <i class="fa-solid fa-book"></i>
                    <span>${c.code}</span>
                  </div>
                  <div class="text-gray-700 text-base">${c.name}</div>
                </div>
                <div class="flex-1 flex flex-col justify-center items-end text-right gap-0.5 text-xs text-gray-500">
                  <div class="flex flex-row gap-2 mb-0.5">
                    <span class="badge badge-morning"><i class="fa-solid fa-calendar-days mr-1"></i>${c.year}/${c.term}</span>
                    <span class="badge ${badgeClass}">${c.examType}</span>
                  </div>
                  <span>ประกาศ ${formatDateTime(c.announceDate)}</span>
                </div>
                <svg class="w-5 h-5 ml-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}" id="chevron-${i}" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7"/></svg>
              </button>

              <div id="course-scores-${i}" class="px-4 pb-1.5 pt-0 text-gray-700 transition-all duration-300 overflow-hidden" style="max-height: ${isOpen ? '1000px' : '0px'};"></div>
            </div>
          `;
        }
        html += '</div></div>'; // Close grid container and info container
        document.getElementById('student-courses').innerHTML = html;
        // แสดงคะแนนถ้ามีการเลือก
        if (Array.isArray(window.selectedCourseIndexes)) {
          for (const idx of window.selectedCourseIndexes) {
            showCourseScoresInline(idx, courses[idx]);
          }
        }
      })
      .withFailureHandler(function(error) {
        document.getElementById('student-courses').innerHTML = '<div class="text-red-500">เกิดข้อผิดพลาด: ' + error.message + '</div>';
        console.error('GAS error:', error);
      })
      .doGetStudentCourses();
    }

    // ฟังก์ชัน toggle แสดง/ซ่อนคะแนนใต้การ์ด
    function toggleCourseScores(idx, sheetName, code, name, announceDate) {
      window.selectedCourseIndexes = Array.isArray(window.selectedCourseIndexes) ? window.selectedCourseIndexes : [];
      const i = window.selectedCourseIndexes.indexOf(idx);
      const chevron = document.getElementById('chevron-' + idx);
      const box = document.getElementById('course-scores-' + idx);
      if (i !== -1) {
        // คลิกซ้ำ = ซ่อน
        window.selectedCourseIndexes.splice(i, 1);
        if (box) {
          box.style.maxHeight = '0px';
          setTimeout(() => { box.innerHTML = ''; }, 300);
        }
        if (chevron) chevron.classList.remove('rotate-180');
        return;
      }
      // เพิ่ม index นี้เข้าไป (multi-expand)
      window.selectedCourseIndexes.push(idx);
      if (chevron) chevron.classList.add('rotate-180');
      showCourseScoresInline(idx, {scoreSheetName: sheetName, code, name, announceDate});

      // เลื่อนหน้าจอไปที่คะแนนที่เปิด
      setTimeout(() => {
        const courseElement = document.getElementById('course-scores-' + idx);
        if (courseElement) {
          courseElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }, 400); // รอให้ animation เสร็จก่อน
    }

    // แสดงคะแนนใต้การ์ดที่เลือก
    function showCourseScoresInline(idx, course) {
      const el = document.getElementById('course-scores-' + idx);
      if (!el) return;
      // preload: ใช้ cache
      const result = window.courseScoresCache[course.scoreSheetName];
      if (!result || !result.scores || result.scores.length === 0) {
        el.innerHTML = '<div class="text-gray-500">ไม่พบคะแนนในรายวิชานี้</div>';
        el.style.maxHeight = '200px';
        return;
      }
      let html = `<div class="p-2">`;
      html += `<div class="border-t border-gray-200 pt-2 mb-2">`;
      html += `<div class="divide-y">`;
      for (const s of result.scores) {
        const sections = s.sections || [];
        let htmlRight = '';
        if (sections.length === 1) {
          htmlRight = `<div class="font-bold text-blue-700 text-right">${sections[0].score}/${sections[0].fullScore}</div>`;
        } else if (sections.length > 1) {
          let totalScore = 0, totalFull = 0, sectionHtml = '';
          for (const sec of sections) {
            if (typeof sec.score === 'number' && typeof sec.fullScore === 'number') {
              totalScore += sec.score;
              totalFull += sec.fullScore;
            } else if (!isNaN(parseFloat(sec.score)) && !isNaN(parseFloat(sec.fullScore))) {
              totalScore += Number(sec.score);
              totalFull += Number(sec.fullScore);
            }
            sectionHtml += `<div class="text-right text-sm"><span class="text-gray-500">${sec.section || 'Score'}:</span> <span class="font-bold text-blue-700">${sec.score}/${sec.fullScore}</span></div>`;
          }
          htmlRight = `<div class="flex flex-col items-end gap-1">` +
            sectionHtml +
            `<div class="text-right text-sm"><span class="text-gray-500">Total Score:</span> <span class="font-bold text-blue-700">${totalScore}/${totalFull}</span></div>` +
          `</div>`;
        }
        
        // กำหนด layout ตามจำนวน sections
        const isMultiSection = sections.length > 1;
        const leftSideHtml = isMultiSection 
          ? `<div class="flex flex-col gap-1">` +
            `<span class="font-medium text-gray-700">${s.studentId}</span>` +
            `<span class="text-gray-600">${s.studentName}</span>` +
            `</div>`
          : `<div class="flex flex-row items-center gap-2">` +
            `<span class="font-medium text-gray-700">${s.studentId}</span>` +
            `<span class="text-gray-600">${s.studentName}</span>` +
            `</div>`;
            
        html += `<div class="flex flex-row items-start justify-between py-2 text-sm">` +
          leftSideHtml +
          htmlRight +
        `</div>`;
      }
      html += '</div>';
      html += '</div>';
      // Stat
      const st = result.stats;
      function formatStat(val) {
        if (val === null || val === undefined || isNaN(val)) return '-';
        return Number.isInteger(val) ? val : val.toFixed(2);
      }
              html += `<div class="border-t border-gray-200 pt-2">`;
        html += `<div class="flex flex-wrap gap-1 text-[10px]">` +
          `<span class="badge badge-blue stat-badge">Mean ${formatStat(st.mean)}</span>` +
          `<span class="badge badge-green stat-badge">SD ${formatStat(st.sd)}</span>` +
          `<span class="badge badge-yellow stat-badge">Min ${formatStat(st.min)}</span>` +
          `<span class="badge badge-red stat-badge">Max ${formatStat(st.max)}</span>` +
          `<span class="badge badge-purple stat-badge">Rank ${st.rank && st.total ? formatRankDisplay(st.rank + '_' + st.total) : '-'}</span>` +
          `</div></div>`;
      el.innerHTML = html;
      el.style.maxHeight = '1000px';
    }

    // เรียกหลัง auth success
    function afterLogin() {
      renderStudentCourses();
    }

    document.addEventListener('DOMContentLoaded', function() {
      // Show spinner immediately when app starts
      document.getElementById('student-courses').innerHTML = `
        <div class="flex flex-col items-center justify-center py-8">
          <div class="spinner spinner-lg text-blue-500"></div>
        </div>
      `;

      google.script.run.withSuccessHandler(function(email) {
        if (!email) {
          document.getElementById('status').innerHTML = '<span class="text-red-500">กรุณาเข้าสู่ระบบด้วยบัญชี @rsu.ac.th</span>';
          document.getElementById('content').innerHTML = '';
          document.getElementById('user-info').innerHTML = '';
        } else {
          document.getElementById('status').innerHTML = '';
          // แสดง email โดยไม่แสดง @rsu.ac.th
          const displayEmail = email.replace('@rsu.ac.th', '');
          
          // สร้างอักษรย่อจาก email
          const initials = displayEmail.split('.').map(part => part.charAt(0).toUpperCase()).join('');
          
          const userInfoHtml = `
            <div class="flex flex-col gap-1">
              <div class="flex items-center gap-2">
                <div class="w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center text-xs font-light text-white">
                  <span>${initials}</span>
                </div>
                <span>${displayEmail}</span>
              </div>
              <button id="instructorModeBtn" class="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 transition-colors hidden">
                Instructor Mode
              </button>
            </div>
          `;

          document.getElementById('user-info').innerHTML = userInfoHtml;
          document.getElementById('user-info-desktop').innerHTML = userInfoHtml;
          document.getElementById('content').innerHTML = '';
          
          // ตรวจสอบสิทธิ์ instructor
          checkInstructorStatus();
          afterLogin();
        }
      }).checkRsuAuth();
    });

    // ตรวจสอบสิทธิ์ instructor
    function checkInstructorStatus() {
      google.script.run.withSuccessHandler(function(result) {
        if (result.isInstructor || result.isAdmin) {
          // แสดงปุ่มในทั้งสองตำแหน่ง (mobile และ desktop)
          const instructorBtns = document.querySelectorAll('#instructorModeBtn');
          instructorBtns.forEach(btn => {
            btn.classList.remove('hidden');
            btn.addEventListener('click', showInstructorDashboard);
          });
          // แสดงหน้าค้นหาแทนหน้าหลักของนักศึกษา
          showInstructorDashboard();
        }
      }).doCheckInstructorStatus();
    }

    // แสดงหน้า Instructor Dashboard
    function showInstructorDashboard() {
      const mainContent = document.getElementById('main-card');
      mainContent.innerHTML = `
        <div class="space-y-6">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-900">Instructor Dashboard</h2>
          </div>
          
          <!-- Navigation Tabs -->
          <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
              <button onclick="showInstructorTab('search')" id="tab-search" class="tab-button border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600">
                <i class="fas fa-search mr-2"></i>ค้นหาคะแนน
              </button>
              <button onclick="showInstructorTab('upload')" id="tab-upload" class="tab-button border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                <i class="fas fa-upload mr-2"></i>อัปโหลดคะแนน
              </button>
              <button onclick="showInstructorTab('manage')" id="tab-manage" class="tab-button border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                <i class="fas fa-cog mr-2"></i>จัดการประกาศ
              </button>
            </nav>
          </div>
          
          <!-- Search Tab Content -->
          <div id="tab-content-search" class="tab-content">
            <div class="bg-white rounded-lg border p-4">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                <i class="fas fa-search mr-2 text-blue-500"></i>ค้นหาคะแนนนักศึกษา
              </h3>
              <div class="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
                <p class="text-xs text-blue-700">
                  <i class="fas fa-circle mr-2" style="font-size: 4px; vertical-align: middle;"></i>
                  ท่านสามารถค้นหาคะแนนของนักศึกษาในรายวิชาที่ท่านเป็นผู้ประกาศเท่านั้น
                </p>
                <p class="text-xs text-blue-700">
                  <i class="fas fa-circle mr-2" style="font-size: 4px; vertical-align: middle;"></i>
                  ทดสอบการประกาศคะแนนโดยค้นหา ID หรือชื่อนักศึกษา หรือลองค้นหาคำว่า "example" เพื่อดูตัวอย่างผลคะแนน
                </p>
              </div>
              <div class="flex gap-2 items-center">
                <div class="relative flex-1">
                  <input
                    type="text"
                    id="studentSearchInput"
                    placeholder="ค้นหาด้วย ID, ชื่อ, หรือนามสกุล..."
                    class="w-full rounded-md border border-gray-300 px-3 py-2 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    style="vertical-align: middle;"
                  >
                  <button
                    id="clearSearchBtn"
                    onclick="clearSearch()"
                    class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors hidden"
                  >
                    <i class="fas fa-times" style="vertical-align: middle;"></i>
                  </button>
                </div>
                <button
                  id="searchBtn"
                  onclick="searchStudentScores()"
                  class="bg-blue-500 text-white px-4 rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center h-10"
                >
                  <i class="fas fa-search" style="vertical-align: middle;"></i>
                </button>
              </div>
            </div>
            <div id="searchResults" class="space-y-4 mt-8"></div>
          </div>
          
          <!-- Upload Tab Content -->
          <div id="tab-content-upload" class="tab-content hidden">
            <div class="space-y-6">


              <!-- Step 1: Download Template -->
              <div class="bg-white rounded-lg border p-6">
                <div class="flex items-center mb-4">
                  <div class="flex items-center justify-center w-8 h-8 bg-green-500 text-white rounded-full text-sm font-bold mr-3 flex-shrink-0">1</div>
                  <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-download mr-2 text-green-500"></i>ดาวน์โหลด Template
                  </h3>
                </div>

                <div class="space-y-4">
                  <div class="bg-gray-50 rounded-md p-4">
                    <ol class="text-sm text-gray-700 space-y-1 list-disc list-inside">
                      <li>กด "ดาวน์โหลด Template" เพื่อรับไฟล์ CSV ตัวอย่าง</li>
                      <li>ดาวน์โหลดรายชื่อนักศึกษาและอีเมลจากระบบ intranet RSU</li>
                      <li>กรอกข้อมูลตามฟอร์มที่กำหนดโดย Excel หรือ Google Sheets</li>
                      <li>บันทึกไฟล์เป็น CSV (UTF-8) เพื่อรองรับภาษาไทย</li>
                    </ol>
                  </div>

                  <div class="text-center">
                    <button
                      id="downloadTemplateBtn"
                      onclick="downloadTemplate()"
                      class="bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600 transition-colors flex items-center mx-auto"
                    >
                      <i class="fas fa-download mr-2"></i>
                      ดาวน์โหลด Template
                    </button>
                  </div>

                  <div class="p-3 bg-blue-50 border border-blue-200 rounded">
                    <ul class="text-xs text-blue-700 list-disc list-inside space-y-1">
                      <li>คอลัมน์คะแนนต้องเรียงเป็น "คะแนนที่ได้" ตามด้วย "คะแนนเต็ม"</li>
                      <li>หากแยกคะแนนเป็นพาร์ท เช่น Part A, Part B ฯลฯ ให้เรียงเป็น Part A คะแนนที่ได้ → Part A คะแนนเต็ม → Part B คะแนนที่ได้ → Part B คะแนนเต็ม ไปเรื่อยๆ ปิดท้ายด้วยคะแนนรวมและคะแนนเต็มรวมในสองคอลัมน์สุดท้าย</li>
                    </ul>
                  </div>
                </div>
              </div>

              <!-- Step 2: File Selection and Preview -->
              <div class="bg-white rounded-lg border p-6">
                <div class="flex items-center mb-4">
                  <div class="flex items-center justify-center w-8 h-8 bg-blue-500 text-white rounded-full text-sm font-bold mr-3 flex-shrink-0">2</div>
                  <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-search mr-2 text-blue-500"></i>กรอกรายละเอียดวิชาและตรวจสอบข้อมูล
                  </h3>
                </div>

                <div>
                  <form id="uploadForm" class="space-y-6">
                    <!-- Course Information -->
                    <div class="bg-gray-50 rounded-md p-4">
                      <h4 class="text-sm font-medium text-gray-800 mb-3">ข้อมูลรายวิชา</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">รหัสวิชา*</label>
                        <input type="text" id="courseCode" required
                               placeholder="เช่น DRD341"
                               class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ชื่อวิชา*</label>
                        <input type="text" id="courseName" required
                               placeholder="เช่น Perio I"
                               class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ประเภทสอบ*</label>
                        <select id="examType" required class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                          <option value="">เลือกประเภทสอบ</option>
                          <option value="midterm" selected>กลางภาค</option>
                          <option value="final">ปลายภาค</option>
                          <option value="quiz">Quiz</option>
                          <option value="practical">Practice</option>
                          <option value="assignment">Assignment</option>
                        </select>
                      </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ปีการศึกษา (ค.ศ.)*</label>
                        <input type="text" id="year" required value="2025"
                               class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ภาคการศึกษา*</label>
                        <select id="term" required class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                          <option value="">เลือกภาคการศึกษา</option>
                          <option value="S">S (มิถุนายน - กรกฎาคม)</option>
                          <option value="1">1 (สิงหาคม - ธันวาคม)</option>
                          <option value="2">2 (มกราคม - พฤษภาคม)</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <!-- File Upload -->
                  <div class="bg-gray-50 rounded-md p-4">
                    <h4 class="text-sm font-medium text-gray-800 mb-3">เลือกไฟล์คะแนน</h4>
                    <label for="scoreFile" class="block cursor-pointer">
                      <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 transition-colors group">
                        <div class="space-y-2">
                          <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 group-hover:text-blue-400 transition-colors"></i>
                          <div class="mt-2">
                            <span class="text-sm font-medium text-blue-600 hover:text-blue-500">เลือกไฟล์ CSV</span>
                          </div>
                          <input type="file" id="scoreFile" accept=".csv" required class="hidden">
                          <div id="fileInfo" class="text-sm text-gray-600 hidden"></div>
                        </div>
                      </div>
                    </label>
                  </div>



                  <!-- File Preview Section -->
                  <div id="filePreview" class="hidden">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 class="text-sm font-medium text-blue-800 mb-3 flex items-center">
                        <i class="fas fa-table mr-2"></i>ข้อมูลในไฟล์
                      </h4>
                      <div id="previewContent" class="text-sm"></div>
                      <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded">
                        <ul class="text-xs text-yellow-700 list-disc list-inside space-y-1">
                          <li>ตรวจสอบชื่อและอีเมลของนักศึกษาให้ถูกต้อง</li>
                          <li>คะแนนและคะแนนเต็มครบถ้วนตามรูปแบบที่กำหนด</li>
                          <li>หากข้อมูลไม่ถูกต้อง กรุณาแก้ไขไฟล์และเลือกใหม่</li>
                        </ul>
                      </div>
                    </div>
                    </div>
                  </form>
                </div>
              </div>

              <!-- Step 3: Upload -->
              <div class="bg-white rounded-lg border p-6">
                <div class="flex items-center mb-4">
                  <div class="flex items-center justify-center w-8 h-8 bg-purple-500 text-white rounded-full text-sm font-bold mr-3 flex-shrink-0">3</div>
                  <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-upload mr-2 text-purple-500"></i>อัปโหลดคะแนน
                  </h3>
                </div>

                <div>
                  <div class="bg-gray-50 rounded-md p-4 mb-4">
                    <p class="text-sm text-gray-700 mb-3">
                      เมื่อตรวจสอบข้อมูลแล้วและมั่นใจว่าถูกต้อง กดปุ่มด้านล่างเพื่อบันทึกคะแนนเข้าระบบ
                    </p>
                  </div>

                  <!-- Upload Results -->
                  <div id="uploadResult"></div>
                </div>

                <!-- Upload Buttons - Outside grey container but within Step 3 -->
                <div class="flex gap-3 justify-center mt-4">
                  <button
                    type="submit"
                    form="uploadForm"
                    id="uploadBtn"
                    class="bg-purple-500 text-white px-8 py-2 rounded-md hover:bg-purple-600 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled
                  >
                    <i class="fas fa-upload mr-2"></i>อัปโหลดคะแนน
                  </button>
                  <button
                    type="button"
                    onclick="resetUploadForm()"
                    class="bg-gray-500 text-white px-6 py-2 rounded-md hover:bg-gray-600 transition-colors flex items-center"
                  >
                    <i class="fas fa-redo mr-2"></i>เริ่มใหม่
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Manage Tab Content -->
          <div id="tab-content-manage" class="tab-content hidden">
            <div class="bg-white rounded-lg border p-4">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                <i class="fas fa-cog mr-2 text-blue-500"></i>จัดการประกาศคะแนน
              </h3>
              <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
                <div class="text-xs text-yellow-700">
                  <p class="mb-2">
                    <i class="fas fa-circle mr-2" style="font-size: 4px; vertical-align: middle;"></i>
                    คะแนนจะแสดงผล 30 วัน หลังจากนั้นจะถูกจะถูกลบโดยอัตโนมัติ
                  </p>
                  <p>
                    <i class="fas fa-circle mr-2" style="font-size: 4px; vertical-align: middle;"></i>
                    กดปุ่มไลน์ เพื่อ Copy ลิงค์ประกาศคะแนน และส่งให้นักศึกษาได้เลย
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Course Cards Container - Outside tabs for wider layout -->
        <div id="courseCardsContainer" class="mt-6 hidden">
          <div id="manageCoursesList" class="space-y-4">
            <!-- Course cards will be loaded here -->
          </div>
        </div>
      `;
      
      // เพิ่ม event listener สำหรับ Enter key และ clear button
      const searchInput = document.getElementById('studentSearchInput');
      const clearBtn = document.getElementById('clearSearchBtn');

      searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          searchStudentScores();
        }
      });

      // แสดง/ซ่อน clear button ตามเนื้อหาใน input
      searchInput.addEventListener('input', function(e) {
        if (e.target.value.trim()) {
          clearBtn.classList.remove('hidden');
        } else {
          clearBtn.classList.add('hidden');
        }
      });
      
      // เพิ่ม event listener สำหรับ upload form
      document.getElementById('uploadForm').addEventListener('submit', function(e) {
        e.preventDefault();
        uploadScores();
      });



      // เพิ่ม event listener สำหรับ file input
      document.getElementById('scoreFile').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const fileInfo = document.getElementById('fileInfo');
        const uploadBtn = document.getElementById('uploadBtn');
        const filePreview = document.getElementById('filePreview');

        if (file) {
          const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB
          fileInfo.innerHTML = `
            <div class="flex items-center justify-center space-x-2">
              <i class="fas fa-file-csv text-green-500"></i>
              <span class="font-medium">${file.name}</span>
              <span class="text-gray-500">(${fileSize} MB)</span>
            </div>
          `;
          fileInfo.classList.remove('hidden');

          // ตรวจสอบขนาดไฟล์
          if (file.size > 5 * 1024 * 1024) { // 5MB
            fileInfo.innerHTML = `
              <div class="flex items-center justify-center space-x-2 text-red-600">
                <i class="fas fa-exclamation-triangle"></i>
                <span>ไฟล์มีขนาดใหญ่เกินไป (${fileSize} MB) กรุณาเลือกไฟล์ที่มีขนาดไม่เกิน 5MB</span>
              </div>
            `;
            e.target.value = ''; // Clear the file input
            uploadBtn.disabled = true;
            filePreview.classList.add('hidden');
          } else {
            // แสดงข้อมูลในไฟล์ทันที
            previewFileAuto();

            // เลื่อนหน้าจอไปที่ preview section เมื่อเลือกไฟล์
            setTimeout(() => {
              const previewDiv = document.getElementById('filePreview');
              if (previewDiv) {
                previewDiv.scrollIntoView({ behavior: 'smooth', block: 'start' });
              }
            }, 300);
          }
        } else {
          fileInfo.classList.add('hidden');
          uploadBtn.disabled = true;
          filePreview.classList.add('hidden');
        }
      });
      
      // แสดง tab แรก
      showInstructorTab('search');

      // ไม่โหลดรายวิชาทันที - จะโหลดเมื่อไปที่ tab manage เท่านั้น

      // ตั้งค่าปีการศึกษาและภาคการศึกษาตามวันที่ปัจจุบัน
      setCurrentAcademicYearAndTerm();

      // เพิ่ม event listener สำหรับการเลื่อนหน้าจอเมื่อพิมพ์ใน section 2
      const courseInfoInputs = ['courseCode', 'courseName', 'examType', 'year', 'term'];
      courseInfoInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
          input.addEventListener('focus', function() {
            setTimeout(() => {
              const courseInfoSection = document.querySelector('.bg-gray-50');
              if (courseInfoSection) {
                courseInfoSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
              }
            }, 100);
          });
        }
      });
    }

    // ฟังก์ชันตั้งค่าปีการศึกษาและภาคการศึกษาตามวันที่ปัจจุบัน
    function setCurrentAcademicYearAndTerm() {
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1; // JavaScript months are 0-indexed

      // ตั้งค่าปีการศึกษา
      document.getElementById('year').value = currentYear;

      // ตั้งค่าภาคการศึกษาตามเดือน
      let selectedTerm = '';
      if (currentMonth >= 6 && currentMonth <= 7) {
        selectedTerm = 'S'; // มิถุนายน - กรกฎาคม
      } else if (currentMonth >= 8 && currentMonth <= 12) {
        selectedTerm = '1'; // สิงหาคม - ธันวาคม
      } else if (currentMonth >= 1 && currentMonth <= 5) {
        selectedTerm = '2'; // มกราคม - พฤษภาคม
      }

      if (selectedTerm) {
        document.getElementById('term').value = selectedTerm;
      }
    }

    // ค้นหาคะแนนนักศึกษา
    function searchStudentScores() {
      const searchInput = document.getElementById('studentSearchInput');
      const searchBtn = document.getElementById('searchBtn');

      if (!searchInput) {
        alert('ไม่พบช่องค้นหา กรุณาลองใหม่');
        return;
      }

      const searchTerm = searchInput.value.trim();

      if (!searchTerm) {
        alert('กรุณากรอกคำค้นหา');
        searchInput.focus();
        return;
      }

      const resultsDiv = document.getElementById('searchResults');

      if (!resultsDiv) {
        alert('ไม่พบพื้นที่แสดงผล กรุณาลองใหม่');
        return;
      }

      // ปิดการใช้งานปุ่มค้นหาและแสดง loading
      searchBtn.disabled = true;
      searchBtn.innerHTML = '<div class="spinner text-white"></div>';

      // ไม่แสดงสถานะการค้นหา - ลบ spinner และข้อความออก
      resultsDiv.innerHTML = '';

      console.log('Calling doSearchStudentScores with searchTerm:', searchTerm);

      google.script.run
        .withSuccessHandler(function(result) {
          console.log('Search success result:', result);
          // เปิดการใช้งานปุ่มค้นหาอีกครั้ง
          searchBtn.disabled = false;
          searchBtn.innerHTML = '<i class="fas fa-search"></i>';

          displaySearchResults(result, searchTerm);
        })
        .withFailureHandler(function(error) {
          console.log('Search failure error:', error);
          // เปิดการใช้งานปุ่มค้นหาอีกครั้ง
          searchBtn.disabled = false;
          searchBtn.innerHTML = '<i class="fas fa-search"></i>';

          resultsDiv.innerHTML = `
            <div class="bg-red-50 border border-red-200 rounded-md p-4">
              <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mt-1 mr-3"></i>
                <div>
                  <h4 class="text-sm font-medium text-red-800">เกิดข้อผิดพลาด</h4>
                  <div class="mt-2 text-sm text-red-700">${error.message}</div>
                  <button onclick="searchStudentScores()" class="mt-2 text-blue-500 hover:text-blue-600 text-sm">
                    <i class="fas fa-redo mr-1"></i>ลองใหม่
                  </button>
                </div>
              </div>
            </div>
          `;
        })
        .doSearchStudentScores(searchTerm);
    }




    // แสดงผลการค้นหา
    function displaySearchResults(result, searchTerm) {
      const resultsDiv = document.getElementById('searchResults');

      if (result.error) {
        resultsDiv.innerHTML = `
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <i class="fas fa-exclamation-circle text-red-400 mt-1 mr-3"></i>
              <div>
                <h4 class="text-sm font-medium text-red-800">เกิดข้อผิดพลาด</h4>
                <div class="mt-2 text-sm text-red-700">${result.error}</div>
              </div>
            </div>
          </div>
        `;
        return;
      }

      if (!result.results || result.results.length === 0) {
        resultsDiv.innerHTML = `
          <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div class="flex">
              <i class="fas fa-search text-yellow-400 mt-1 mr-3"></i>
              <div>
                <h4 class="text-sm font-medium text-yellow-800">ไม่พบผลการค้นหา</h4>
                <div class="mt-2 text-sm text-yellow-700">
                  ไม่พบนักศึกษาที่ตรงกับคำค้นหา "<strong>${searchTerm}</strong>" ในรายวิชาที่คุณเป็นผู้ประกาศ
                </div>
              </div>
            </div>
          </div>
        `;
        return;
      }

      let html = '<div class="space-y-4">';

      for (let i = 0; i < result.results.length; i++) {
        const student = result.results[i];
        html += renderStudentSearchResult(student);
      }

      html += '</div>';
      resultsDiv.innerHTML = html;
    }

    // แสดงผลคะแนนนักศึกษาแต่ละคน - ใช้รูปแบบเดียวกับ student mode
    function renderStudentSearchResult(student) {
      // ใช้รูปแบบเดียวกับ student mode card ทุกประการ
      let html = `
        <div class="rounded-xl shadow bg-white mb-3 border border-gray-100">
          <div class="w-full flex flex-row justify-between items-center px-4 py-1.5">
            <div class="flex flex-col justify-center text-left h-full gap-0.5">
              <div class="font-bold text-blue-700 flex items-center gap-2">
                <i class="fa-solid fa-book"></i>
                <span>${student.course.code}</span>
              </div>
              <div class="text-gray-700 text-base">${student.course.name}</div>
            </div>
            <div class="flex flex-col justify-center items-end text-right gap-0.5 text-xs text-gray-500">
              <div class="flex flex-row gap-2 mb-0.5">
                <span class="badge badge-morning"><i class="fa-solid fa-calendar-days mr-1"></i>${student.course.year}/${student.course.term}</span>
                <span class="badge ${student.course.examType === 'midterm' ? 'badge-blue' : student.course.examType === 'final' ? 'badge-red' : 'badge-gray'}">${student.course.examType}</span>
              </div>
              <span>ประกาศ ${formatDateTime(student.course.announceDate)}</span>
            </div>
          </div>

          <div class="px-4 pb-1.5 pt-0 text-gray-700 transition-all duration-300 overflow-hidden" style="max-height: 1000px;">
            <div class="p-2">
              <div class="border-t border-gray-200 pt-2 mb-2">
                <div class="divide-y">
      `;

      // แสดงคะแนนแต่ละ section ในรูปแบบเดียวกับ student mode
      if (student.sections && student.sections.length > 0) {
        const sections = student.sections;

        // สร้าง htmlRight ตามจำนวน sections
        let htmlRight = '';
        if (sections.length === 1) {
          htmlRight = `<div class="font-bold text-blue-700 text-right">${sections[0].score}/${sections[0].fullScore}</div>`;
        } else if (sections.length > 1) {
          let totalScore = 0, totalFull = 0, sectionHtml = '';
          for (const sec of sections) {
            if (typeof sec.score === 'number' && typeof sec.fullScore === 'number') {
              totalScore += sec.score;
              totalFull += sec.fullScore;
            } else if (!isNaN(parseFloat(sec.score)) && !isNaN(parseFloat(sec.fullScore))) {
              totalScore += Number(sec.score);
              totalFull += Number(sec.fullScore);
            }
            sectionHtml += `<div class="text-right text-sm"><span class="text-gray-500">${sec.section || 'Score'}:</span> <span class="font-bold text-blue-700">${sec.score}/${sec.fullScore}</span></div>`;
          }
          htmlRight = `<div class="flex flex-col items-end gap-1">` +
            sectionHtml +
            `<div class="text-right text-sm"><span class="text-gray-500">Total Score:</span> <span class="font-bold text-blue-700">${totalScore}/${totalFull}</span></div>` +
          `</div>`;
        }

        // กำหนด layout ตามจำนวน sections
        const isMultiSection = sections.length > 1;
        const leftSideHtml = isMultiSection
          ? `<div class="flex flex-col gap-1">` +
            `<span class="font-medium text-gray-700">${student.studentId}</span>` +
            `<span class="text-gray-600">${student.studentName}</span>` +
            `</div>`
          : `<div class="flex flex-row items-center gap-2">` +
            `<span class="font-medium text-gray-700">${student.studentId}</span>` +
            `<span class="text-gray-600">${student.studentName}</span>` +
            `</div>`;

        html += `<div class="flex flex-row items-start justify-between py-2 text-sm">` +
          leftSideHtml +
          htmlRight +
        `</div>`;
      }

      html += `
                </div>
              </div>
      `;

      // แสดงสถิติเหมือนใน student mode
      if (student.stats && Object.keys(student.stats).length > 0) {
        const st = student.stats;
        html += `<div class="border-t border-gray-200 pt-2">`;
        html += `<div class="flex flex-wrap gap-1 text-[10px]">` +
          `<span class="badge badge-blue stat-badge">Mean ${formatStat(st.mean)}</span>` +
          `<span class="badge badge-green stat-badge">SD ${formatStat(st.sd)}</span>` +
          `<span class="badge badge-yellow stat-badge">Min ${formatStat(st.min)}</span>` +
          `<span class="badge badge-red stat-badge">Max ${formatStat(st.max)}</span>` +
          `<span class="badge badge-purple stat-badge">Rank ${st.rank && st.total ? formatRankDisplay(st.rank + '_' + st.total) : '-'}</span>` +
          `</div></div>`;
      }

      html += `
            </div>
          </div>
        </div>
      `;

      return html;
    }

    // ล้างการค้นหา
    function clearSearch() {
      const searchInput = document.getElementById('studentSearchInput');
      const clearBtn = document.getElementById('clearSearchBtn');
      const resultsDiv = document.getElementById('searchResults');

      searchInput.value = '';
      clearBtn.classList.add('hidden');
      if (resultsDiv) {
        resultsDiv.innerHTML = '';
      }
      searchInput.focus();
    }

    // จัดการ Enter key ในช่องค้นหา
    document.addEventListener('DOMContentLoaded', function() {
      const searchInput = document.getElementById('studentSearchInput');
      const clearBtn = document.getElementById('clearSearchBtn');

      if (searchInput) {
        // จัดการ Enter key
        searchInput.addEventListener('keypress', function(e) {
          if (e.key === 'Enter') {
            e.preventDefault();
            searchStudentScores();
          }
        });

        // แสดง/ซ่อนปุ่ม clear
        searchInput.addEventListener('input', function() {
          if (this.value.trim()) {
            clearBtn.classList.remove('hidden');
          } else {
            clearBtn.classList.add('hidden');
          }
        });
      }
    });



    // ฟังก์ชันจัดรูปแบบวันที่และเวลา
    function formatDateTime(dateString) {
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return 'Invalid date';
        }

        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');

        return `${day}/${month}/${year} ${hours}:${minutes}`;
      } catch (error) {
        console.error('Error formatting date:', dateString, error);
        return 'Error formatting date';
      }
    }

    // ฟังก์ชันจัดรูปแบบวันที่และเวลาแบบไม่มี zero padding
    function formatDateTimeNoPad(dateString) {
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return 'Invalid date';
        }

        const day = date.getDate();
        const month = date.getMonth() + 1;
        const year = date.getFullYear();
        const hours = date.getHours();
        const minutes = date.getMinutes().toString().padStart(2, '0'); // เฉพาะนาทีใช้ zero pad

        return `${day}/${month}/${year} ${hours}:${minutes}`;
      } catch (error) {
        console.error('Error formatting date:', dateString, error);
        return 'Error formatting date';
      }
    }

    // ฟังก์ชันแปลงรูปแบบ rank จาก 2_3 เป็น 2/3 สำหรับแสดงผล
    function formatRankDisplay(rankString) {
      if (!rankString) return '-';

      // แปลง 2_3 เป็น 2/3 สำหรับแสดงผล
      if (typeof rankString === 'string' && rankString.includes('_')) {
        return rankString.replace('_', '/');
      }

      // ถ้าเป็นรูปแบบเก่า (2/3) ให้คืนค่าเดิม
      return rankString;
    }

    // ฟังก์ชันจัดรูปแบบสถิติ
    function formatStat(val) {
      if (val === null || val === undefined || isNaN(val)) return '-';
      return Number.isInteger(val) ? val : Number(val).toFixed(2);
    }

    // กลับไปหน้าหลัก
    function showStudentView() {
      renderStudentCourses();
    }

    // === Instructor Dashboard Functions ===

    // แสดง tab ต่างๆ ใน instructor dashboard
    function showInstructorTab(tabName) {
      // ซ่อน content ทั้งหมด
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
      });

      // ลบ active state จาก tab ทั้งหมด
      document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
      });

      // แสดง content ที่เลือก
      document.getElementById('tab-content-' + tabName).classList.remove('hidden');

      // ตั้งค่า active state ให้ tab ที่เลือก
      document.getElementById('tab-' + tabName).classList.remove('border-transparent', 'text-gray-500');
      document.getElementById('tab-' + tabName).classList.add('border-blue-500', 'text-blue-600');

      // เลื่อนหน้าจอไปที่ tab content (ยกเว้น upload tab)
      if (tabName !== 'upload') {
        setTimeout(() => {
          const tabContent = document.getElementById('tab-content-' + tabName);
          if (tabContent) {
            tabContent.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }, 100);
      }

      // ล้างการค้นหาเมื่อเปลี่ยน tab
      clearSearch();

      // จัดการ course cards container
      const courseCardsContainer = document.getElementById('courseCardsContainer');
      if (tabName === 'manage') {
        // แสดง course cards container
        courseCardsContainer.classList.remove('hidden');

        // แสดง loading modal
        showLoadingModal();

        loadInstructorCourses();
      } else {
        // ซ่อน course cards container สำหรับ tab อื่น
        courseCardsContainer.classList.add('hidden');
      }
    }

    // ดาวน์โหลด template
    function downloadTemplate() {
      const downloadBtn = document.getElementById('downloadTemplateBtn');

      // แสดง loading modal และปิดการใช้งานปุ่ม
      downloadBtn.disabled = true;
      showLoadingModal();

      google.script.run
        .withSuccessHandler(function(result) {
          // ซ่อน loading modal
          hideLoadingModal();

          if (result.error) {
            // รีเซ็ตปุ่ม
            downloadBtn.disabled = false;
            alert('เกิดข้อผิดพลาด: ' + result.error);
            return;
          }

          // ใช้ CSV content ที่สร้างจาก server
          const csvContent = result;

          // สร้างและดาวน์โหลดไฟล์
          const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
          const link = document.createElement('a');
          const url = URL.createObjectURL(blob);
          link.setAttribute('href', url);
          link.setAttribute('download', 'score_template.csv');
          link.style.visibility = 'hidden';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // รีเซ็ตปุ่ม
          downloadBtn.disabled = false;
        })
        .withFailureHandler(function(error) {
          // ซ่อน loading modal และรีเซ็ตปุ่ม
          hideLoadingModal();
          downloadBtn.disabled = false;

          alert('เกิดข้อผิดพลาด: ' + error.message);
        })
        .doDownloadCSVTemplate();
    }

    // ตรวจสอบข้อมูลไฟล์อัตโนมัติเมื่อเลือกไฟล์
    function previewFileAuto() {
      const fileInput = document.getElementById('scoreFile');
      const previewDiv = document.getElementById('filePreview');
      const previewContent = document.getElementById('previewContent');
      const uploadBtn = document.getElementById('uploadBtn');

      if (!fileInput.files[0]) {
        return;
      }

      const file = fileInput.files[0];

      // แสดง loading
      previewContent.innerHTML = `
        <div class="text-center p-4">
          <div class="spinner spinner-lg text-blue-500 mb-2"></div>
          <div class="text-gray-500">กำลังอ่านไฟล์...</div>
        </div>
      `;
      previewDiv.classList.remove('hidden');

      // ใช้ PapaParse สำหรับประมวลผล CSV
      Papa.parse(file, {
        header: false,
        skipEmptyLines: true,
        encoding: "UTF-8",
        complete: function(results) {
          try {
            if (results.errors.length > 0) {
              previewContent.innerHTML = `
                <div class="text-red-600 text-sm">
                  <i class="fas fa-exclamation-circle mr-2"></i>
                  พบข้อผิดพลาดในไฟล์: ${results.errors[0].message}
                </div>
              `;
              uploadBtn.disabled = true;
              return;
            }

            const scoreData = results.data;
            if (!scoreData || scoreData.length < 2) {
              previewContent.innerHTML = `
                <div class="text-yellow-600 text-sm">
                  <i class="fas fa-exclamation-triangle mr-2"></i>
                  ไฟล์ไม่มีข้อมูลคะแนนเพียงพอ (ต้องมีอย่างน้อย 2 แถว)
                </div>
              `;
              uploadBtn.disabled = true;
              return;
            }

            // แสดงข้อมูลทั้งหมดพร้อม scroll
            let html = '<div class="overflow-x-auto">';
            html += '<div class="max-h-80 overflow-y-auto border border-gray-200 rounded">';
            html += '<table class="min-w-full text-xs">';

            scoreData.forEach((row, index) => {
              html += '<tr class="' + (index === 0 ? 'bg-gray-100 font-medium sticky top-0' : 'hover:bg-gray-50') + '">';
              row.forEach((cell, cellIndex) => {
                const cellContent = String(cell || '').substring(0, 25) + (String(cell || '').length > 25 ? '...' : '');
                html += `<td class="border-b border-gray-200 px-3 py-2 ${index === 0 ? 'font-medium bg-gray-100' : ''}">${cellContent}</td>`;
              });
              html += '</tr>';
            });

            html += '</table>';
            html += '</div></div>';

            html += `<div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
              <div class="flex items-center justify-between text-sm">
                <div class="text-blue-800">
                  <strong>สรุปข้อมูล:</strong> ${scoreData.length - 1} นักศึกษา, ${scoreData[0].length} คอลัมน์
                </div>
                <div class="text-blue-600 text-xs">
                  <i class="fas fa-up-down mr-1"></i>เลื่อนเพื่อตรวจสอบข้อมูลทั้งหมด
                </div>
              </div>
            </div>`;

            previewContent.innerHTML = html;
            uploadBtn.disabled = false;

          } catch (error) {
            previewContent.innerHTML = `
              <div class="text-red-600 text-sm">
                <i class="fas fa-exclamation-circle mr-2"></i>
                เกิดข้อผิดพลาด: ${error.message}
              </div>
            `;
            uploadBtn.disabled = true;
          }
        },
        error: function(error) {
          previewContent.innerHTML = `
            <div class="text-red-600 text-sm">
              <i class="fas fa-exclamation-circle mr-2"></i>
              ไม่สามารถอ่านไฟล์ได้: ${error.message}
            </div>
          `;
          uploadBtn.disabled = true;
        }
      });
    }

    // อัปโหลดคะแนน
    function uploadScores() {
      const form = document.getElementById('uploadForm');
      const fileInput = document.getElementById('scoreFile');
      const resultDiv = document.getElementById('uploadResult');

      // ตรวจสอบไฟล์
      if (!fileInput.files[0]) {
        alert('กรุณาเลือกไฟล์คะแนน');
        return;
      }

      // ตรวจสอบว่าปุ่มอัปโหลดพร้อมใช้งานหรือไม่
      const uploadBtn = document.getElementById('uploadBtn');
      if (uploadBtn.disabled) {
        alert('กรุณาเลือกไฟล์ที่ถูกต้องก่อนอัปโหลด');
        return;
      }

      const file = fileInput.files[0];

      // แสดง loading modal
      showLoadingModal();

      // ใช้ PapaParse สำหรับประมวลผล CSV (อีกครั้งเพื่อความปลอดภัย)
      Papa.parse(file, {
        header: false,
        skipEmptyLines: true,
        encoding: "UTF-8",
        complete: function(results) {
          try {
            // ตรวจสอบ parsing errors
            if (results.errors.length > 0) {
              console.error("CSV Parsing Errors:", results.errors);

              // แสดงรายละเอียดข้อผิดพลาดที่เป็นประโยชน์
              const errorMessages = results.errors.map(error => {
                let message = `แถว ${error.row + 1}: `;
                switch(error.type) {
                  case 'Quotes':
                    message += 'มีปัญหาเกี่ยวกับเครื่องหมายคำพูด (") ในข้อมูล';
                    break;
                  case 'Delimiter':
                    message += 'ไม่พบตัวคั่น (comma) ที่ถูกต้อง';
                    break;
                  case 'FieldMismatch':
                    message += 'จำนวนคอลัมน์ไม่ตรงกับแถวอื่น';
                    break;
                  default:
                    message += error.message || 'ข้อผิดพลาดไม่ทราบสาเหตุ';
                }
                return message;
              }).slice(0, 5); // แสดงแค่ 5 ข้อผิดพลาดแรก

              resultDiv.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                  <div class="flex">
                    <i class="fas fa-exclamation-circle text-red-400 mt-1 mr-3"></i>
                    <div>
                      <h4 class="text-sm font-medium text-red-800">เกิดข้อผิดพลาดในการอ่านไฟล์ CSV</h4>
                      <div class="mt-2 text-sm text-red-700">
                        <p class="mb-2">พบข้อผิดพลาด ${results.errors.length} รายการ:</p>
                        <ul class="list-disc list-inside space-y-1">
                          ${errorMessages.map(msg => `<li>${msg}</li>`).join('')}
                          ${results.errors.length > 5 ? `<li>และอีก ${results.errors.length - 5} รายการ...</li>` : ''}
                        </ul>
                        <p class="mt-3 font-medium">คำแนะนำ:</p>
                        <ul class="list-disc list-inside space-y-1">
                          <li>ตรวจสอบว่าไฟล์เป็น CSV ที่ถูกต้อง</li>
                          <li>ใช้ UTF-8 encoding เมื่อบันทึกไฟล์</li>
                          <li>ตรวจสอบว่าทุกแถวมีจำนวนคอลัมน์เท่ากัน</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              `;
              return;
            }

            const scoreData = results.data;

            // ตรวจสอบข้อมูลพื้นฐาน
            if (!scoreData || scoreData.length < 2) {
              resultDiv.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                  <div class="flex">
                    <i class="fas fa-exclamation-circle text-red-400 mt-1 mr-3"></i>
                    <div>
                      <h4 class="text-sm font-medium text-red-800">ไฟล์ไม่มีข้อมูลคะแนน</h4>
                      <div class="mt-2 text-sm text-red-700">กรุณาตรวจสอบไฟล์และลองใหม่</div>
                    </div>
                  </div>
                </div>
              `;
              return;
            }

            // เตรียมข้อมูลสำหรับอัปโหลด
            const uploadData = {
              courseCode: document.getElementById('courseCode').value,
              courseName: document.getElementById('courseName').value,
              examType: document.getElementById('examType').value,
              year: document.getElementById('year').value,
              term: document.getElementById('term').value,
              scoreData: scoreData,
              isActive: true // ตั้งค่าเริ่มต้นเป็น active
            };

            // ส่งข้อมูลไปยัง server พร้อม timeout handling
            let uploadTimeout = setTimeout(() => {
              // Keep showing the same spinner for timeout - no text changes needed
            }, 10000); // แสดงข้อความหลังจาก 10 วินาที

            google.script.run
              .withSuccessHandler(function(result) {
                clearTimeout(uploadTimeout);
                hideLoadingModal();

                if (result.error) {
                  // ตรวจสอบว่าเป็น DEADLINE_EXCEEDED error หรือไม่
                  const isTimeoutError = result.error.includes('DEADLINE_EXCEEDED') ||
                                       result.error.includes('timeout') ||
                                       result.error.includes('server error');

                  resultDiv.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-md p-4">
                      <div class="flex">
                        <i class="fas fa-exclamation-circle text-red-400 mt-1 mr-3"></i>
                        <div>
                          <h4 class="text-sm font-medium text-red-800">เกิดข้อผิดพลาด</h4>
                          <div class="mt-2 text-sm text-red-700">
                            <p>${result.error}</p>
                            ${isTimeoutError ? `
                              <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
                                <p class="font-medium text-blue-800">คำแนะนำสำหรับปัญหา Timeout:</p>
                                <ul class="list-disc list-inside mt-1 space-y-1 text-blue-700">
                                  <li>ลองลดขนาดไฟล์โดยแบ่งข้อมูลออกเป็นหลายไฟล์</li>
                                  <li>ตรวจสอบว่าข้อมูลในไฟล์ถูกต้องและไม่มีข้อผิดพลาด</li>
                                  <li>ลองอัปโหลดใหม่อีกครั้งหลังจากรอสักครู่</li>
                                  <li>หากยังมีปัญหา กรุณาติดต่อผู้ดูแลระบบ</li>
                                </ul>
                              </div>
                            ` : ''}
                          </div>
                        </div>
                      </div>
                    </div>
                  `;
                } else {
                  // แสดง success modal แทนการแสดงผลในหน้า
                  showSuccessModal();

                  // ล้างผลลัพธ์และรีเซ็ตฟอร์ม
                  resultDiv.innerHTML = '';
                  setTimeout(() => {
                    resetUploadForm();
                  }, 1000);
                }
              })
              .withFailureHandler(function(error) {
                clearTimeout(uploadTimeout);
                hideLoadingModal();

                const isTimeoutError = error.message.includes('DEADLINE_EXCEEDED') ||
                                     error.message.includes('timeout') ||
                                     error.message.includes('server error');

                resultDiv.innerHTML = `
                  <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="flex">
                      <i class="fas fa-exclamation-circle text-red-400 mt-1 mr-3"></i>
                      <div>
                        <h4 class="text-sm font-medium text-red-800">เกิดข้อผิดพลาดในการเชื่อมต่อ</h4>
                        <div class="mt-2 text-sm text-red-700">
                          <p>${error.message}</p>
                          ${isTimeoutError ? `
                            <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
                              <p class="font-medium text-blue-800">คำแนะนำสำหรับปัญหา Timeout:</p>
                              <ul class="list-disc list-inside mt-1 space-y-1 text-blue-700">
                                <li>ตรวจสอบการเชื่อมต่ออินเทอร์เน็ต</li>
                                <li>ลองรีเฟรชหน้าเว็บและอัปโหลดใหม่</li>
                                <li>ลดขนาดไฟล์หรือแบ่งข้อมูลออกเป็นหลายไฟล์</li>
                                <li>หากยังมีปัญหา กรุณาติดต่อผู้ดูแลระบบ</li>
                              </ul>
                            </div>
                          ` : ''}
                        </div>
                      </div>
                    </div>
                  </div>
                `;
              })
              .doUploadScores(uploadData);

          } catch (error) {
            resultDiv.innerHTML = `
              <div class="bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                  <i class="fas fa-exclamation-circle text-red-400 mt-1 mr-3"></i>
                  <div>
                    <h4 class="text-sm font-medium text-red-800">เกิดข้อผิดพลาดในการประมวลผล</h4>
                    <div class="mt-2 text-sm text-red-700">
                      <p>ข้อผิดพลาด: ${error.message}</p>
                      <p class="mt-2 font-medium">กรุณาลองใหม่อีกครั้ง หรือติดต่อผู้ดูแลระบบ</p>
                    </div>
                  </div>
                </div>
              </div>
            `;
          }
        },
        error: function(error) {
          resultDiv.innerHTML = `
            <div class="bg-red-50 border border-red-200 rounded-md p-4">
              <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mt-1 mr-3"></i>
                <div>
                  <h4 class="text-sm font-medium text-red-800">เกิดข้อผิดพลาดในการอ่านไฟล์</h4>
                  <div class="mt-2 text-sm text-red-700">
                    <p>ไม่สามารถอ่านไฟล์ CSV ได้: ${error.message || 'ไม่ทราบสาเหตุ'}</p>
                    <p class="mt-3 font-medium">คำแนะนำ:</p>
                    <ul class="list-disc list-inside space-y-1">
                      <li>ตรวจสอบว่าไฟล์เป็น CSV ที่ถูกต้อง</li>
                      <li>ลองเปิดไฟล์ด้วย Text Editor เพื่อดูเนื้อหา</li>
                      <li>ตรวจสอบขนาดไฟล์ (ไม่ควรเกิน 5MB)</li>
                      <li>ลองใช้ไฟล์ CSV อื่นเพื่อทดสอบ</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          `;
        }
      });
    }

    // รีเซ็ตฟอร์มอัปโหลด
    function resetUploadForm() {
      document.getElementById('uploadForm').reset();
      document.getElementById('uploadResult').innerHTML = '';
      document.getElementById('fileInfo').classList.add('hidden');
      document.getElementById('filePreview').classList.add('hidden');
      document.getElementById('uploadBtn').disabled = true;

      // ตั้งค่าปีการศึกษาและภาคการศึกษาใหม่
      setCurrentAcademicYearAndTerm();

      // ตั้งค่าประเภทสอบเป็นค่าเริ่มต้น
      document.getElementById('examType').value = 'midterm';
    }

    // โหลดรายวิชาของอาจารย์สำหรับจัดการ
    function loadInstructorCourses() {
      const coursesListDiv = document.getElementById('manageCoursesList');
      
      google.script.run
        .withSuccessHandler(function(courses) {
          // ซ่อน loading modal
          hideLoadingModal();

          if (courses.error) {
            coursesListDiv.innerHTML = `<div class="text-red-500 text-center p-4">${courses.error}</div>`;
            return;
          }

          if (courses.length === 0) {
            coursesListDiv.innerHTML = `
              <div class="text-center py-8">
                <i class="fas fa-inbox text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">ยังไม่มีรายวิชาที่ประกาศคะแนน</p>
              </div>
            `;
            return;
          }

          // เรียงลำดับตามวันที่ประกาศ (ใหม่ไว้ข้างบน)
          courses.sort((a, b) => new Date(b.announceDate) - new Date(a.announceDate));

          console.log('Courses data:', courses); // Debug log

          let html = '<div class="space-y-4">';
          courses.forEach((course, index) => {
            // เช็คว่าเป็นรายการใหม่หรือไม่ (ประกาศภายใน 5 วินาที)
            const isNew = (new Date() - new Date(course.announceDate)) < 5000;
            const highlightClass = isNew ? 'highlight-new' : '';

            console.log('Course:', course.code, 'isActive:', course.isActive, 'type:', typeof course.isActive); // Debug log

            const cardClass = course.isActive !== false ? 'border-gray-200' : 'border-gray-300 bg-gray-50 opacity-75';
            const textClass = course.isActive !== false ? 'text-gray-900' : 'text-gray-500';
            const subTextClass = course.isActive !== false ? 'text-gray-600' : 'text-gray-400';

            html += `
              <div class="rounded-xl shadow mb-3 border ${cardClass} ${highlightClass}" data-course-id="${course.scoreSheetName}">
                <div class="w-full flex flex-row justify-between items-center px-4 py-1.5">
                  <div class="flex flex-col justify-center text-left h-full gap-0.5">
                    <div class="font-bold flex items-center gap-2 ${course.isActive !== false ? 'text-blue-700' : 'text-gray-400'}">
                      <i class="fa-solid fa-book ${course.isActive !== false ? 'text-blue-500' : 'text-gray-400'}"></i>
                      <span>${course.code}</span>
                    </div>
                    <div class="${textClass} text-base">${course.name}</div>
                  </div>
                  <div class="flex flex-col justify-center items-end text-right gap-0.5 text-xs ${subTextClass}">
                    <div class="flex flex-row gap-2 mb-0.5">
                      <span class="badge ${course.isActive !== false ? 'badge-morning' : 'bg-gray-100 text-gray-500 ring-gray-400/20'}"><i class="fa-solid fa-calendar-days mr-1"></i>${course.year}/${course.term}</span>
                      <span class="badge ${course.isActive !== false ? (course.examType === 'midterm' ? 'badge-blue' : course.examType === 'final' ? 'badge-red' : 'badge-gray') : 'bg-gray-100 text-gray-500 ring-gray-400/20'}">${course.examType}</span>
                    </div>
                    <span>ประกาศ ${formatDateTime(course.announceDate)}</span>
                  </div>
                </div>

                <div class="px-4 pb-1.5 pt-0 text-gray-700">
                  <div class="border-t border-gray-200 pt-2">
                    <div class="flex justify-between items-center">
                      <div class="flex gap-2">
                        <button
                          onclick="copyExamAnnouncement('${course.code}', '${course.name}', '${course.year}/${course.term}', '${course.examType}')"
                          class="badge badge-green px-2 py-1 text-xs hover:bg-green-100 transition-colors cursor-pointer"
                          title="Copy ประกาศผลสอบ"
                        >
                          <i class="fa-brands fa-line"></i>
                        </button>
                        <button
                          onclick="viewScoreTable('${course.scoreSheetName}', '${course.code}', '${course.name}')"
                          class="badge badge-blue px-2 py-1 text-xs hover:bg-blue-100 transition-colors cursor-pointer"
                          title="ดูตารางคะแนน"
                        >
                          <i class="fas fa-eye"></i>
                        </button>
                        <button
                          onclick="deleteAnnouncement('${course.scoreSheetName}')"
                          class="badge badge-red px-2 py-1 text-xs hover:bg-red-100 transition-colors cursor-pointer"
                          title="ลบ"
                        >
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                      <div class="toggle-switch ${course.isActive !== false ? 'active' : ''}" onclick="handleToggleClick(event, '${course.scoreSheetName}')" data-course-id="${course.scoreSheetName}">
                        <div class="toggle-slider"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            `;
          });

          html += '</div>';
          coursesListDiv.innerHTML = html;

        })
        .withFailureHandler(function(error) {
          hideLoadingModal();
          coursesListDiv.innerHTML = `<div class="text-red-500 text-center p-4">เกิดข้อผิดพลาด: ${error.message}</div>`;
        })
        .doGetInstructorCourses();
    }

    // Handle toggle click with proper state management
    function handleToggleClick(event, scoreSheetName) {
      // Prevent event bubbling
      event.stopPropagation();

      // Get the toggle element
      const toggleElement = event.currentTarget;
      const currentState = toggleElement.classList.contains('active');

      // Show loading modal
      showLoadingModal();

      // Disable the toggle during processing
      toggleElement.style.pointerEvents = 'none';
      toggleElement.style.opacity = '0.6';

      google.script.run
        .withSuccessHandler(function(result) {
          hideLoadingModal();
          toggleElement.style.pointerEvents = 'auto';
          toggleElement.style.opacity = '1';

          if (result.error) {
            alert('เกิดข้อผิดพลาด: ' + result.error);
            return;
          }

          // Toggle the visual state only after successful server response
          if (currentState) {
            toggleElement.classList.remove('active');
          } else {
            toggleElement.classList.add('active');
          }

          // Update the card styling without full reload
          updateCourseStatusBadge(scoreSheetName, !currentState);

          // Optional: Reload the courses list to reflect changes
          setTimeout(() => {
            loadInstructorCourses();
          }, 500);
        })
        .withFailureHandler(function(error) {
          hideLoadingModal();
          toggleElement.style.pointerEvents = 'auto';
          toggleElement.style.opacity = '1';
          alert('เกิดข้อผิดพลาด: ' + error.message);
        })
        .doManageScoreAnnouncement('toggle', scoreSheetName);
    }

    // Update course status styling without full reload
    function updateCourseStatusBadge(scoreSheetName, isActive) {
      const courseElement = document.querySelector(`[data-course-id="${scoreSheetName}"]`);
      if (courseElement) {
        // Update card styling
        if (isActive) {
          courseElement.className = courseElement.className.replace(/border-gray-300 bg-gray-50 opacity-75/g, 'border-gray-200');
        } else {
          courseElement.className = courseElement.className.replace(/border-gray-200/g, 'border-gray-300 bg-gray-50 opacity-75');
        }

        // Update text colors
        const title = courseElement.querySelector('h4');
        const descriptions = courseElement.querySelectorAll('p');

        if (title) {
          title.className = title.className.replace(/text-gray-(900|500)/g, isActive ? 'text-gray-900' : 'text-gray-500');
        }

        descriptions.forEach(p => {
          p.className = p.className.replace(/text-gray-(600|400)/g, isActive ? 'text-gray-600' : 'text-gray-400');
        });
      }
    }

    // ลบการประกาศคะแนน
    function deleteAnnouncement(scoreSheetName) {
      showConfirmDeleteModal(scoreSheetName);
    }

    // Copy ประกาศผลสอบ to clipboard
    function copyExamAnnouncement(courseCode, courseName, yearTerm, examType) {
      const announcement = `ประกาศผลสอบ ${courseCode} ${courseName}\n${yearTerm} ${examType}\nhttps://dent-rsu.netlify.app/scarescore`;

      navigator.clipboard.writeText(announcement).then(function() {
        // แสดง toast notification
        showToast('คัดลอกประกาศผลสอบเรียบร้อย', 'success');
      }).catch(function(err) {
        console.error('Could not copy text: ', err);
        showToast('ไม่สามารถคัดลอกได้', 'error');
      });
    }

    // ดูตารางคะแนน
    function viewScoreTable(scoreSheetName, courseCode, courseName) {
      showScoreTableModal(scoreSheetName, courseCode, courseName);
    }

    // แสดง toast notification
    function showToast(message, type = 'info') {
      const toast = document.createElement('div');
      toast.className = `fixed top-4 left-1/2 transform -translate-x-1/2 z-50 px-4 py-2 rounded-md text-white text-sm text-center transition-all duration-300 ${
        type === 'success' ? 'bg-gray-700 bg-opacity-90' :
        type === 'error' ? 'bg-red-500 bg-opacity-90' :
        'bg-gray-700 bg-opacity-90'
      }`;
      toast.textContent = message;

      document.body.appendChild(toast);

      // Auto remove after 3 seconds
      setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
          document.body.removeChild(toast);
        }, 300);
      }, 3000);
    }

    // Loading Modal Functions
    function showLoadingModal() {
      const modal = document.getElementById('loadingModal');
      const backdrop = document.getElementById('loadingBackdrop');

      backdrop.classList.remove('hidden');
      modal.classList.remove('hidden');

      setTimeout(() => {
        modal.classList.add('visible');
      }, 10);
    }

    function hideLoadingModal() {
      const modal = document.getElementById('loadingModal');
      const backdrop = document.getElementById('loadingBackdrop');

      modal.classList.remove('visible');

      setTimeout(() => {
        modal.classList.add('hidden');
        backdrop.classList.add('hidden');
      }, 300);
    }

    // Success Modal Functions
    function showSuccessModal() {
      const modal = document.getElementById('successModal');
      const backdrop = document.getElementById('successBackdrop');

      backdrop.classList.remove('hidden');
      modal.classList.remove('hidden');

      setTimeout(() => {
        modal.classList.add('visible');
      }, 10);
    }

    function hideSuccessModal() {
      const modal = document.getElementById('successModal');
      const backdrop = document.getElementById('successBackdrop');

      modal.classList.remove('visible');

      setTimeout(() => {
        modal.classList.add('hidden');
        backdrop.classList.add('hidden');
      }, 300);
    }

    // Confirm Delete Modal Functions
    let deleteScoreSheetName = '';

    function showConfirmDeleteModal(scoreSheetName) {
      deleteScoreSheetName = scoreSheetName;
      const modal = document.getElementById('confirmDeleteModal');
      const backdrop = document.getElementById('confirmDeleteBackdrop');

      backdrop.classList.remove('hidden');
      modal.classList.remove('hidden');

      setTimeout(() => {
        modal.classList.add('visible');
      }, 10);
    }

    function hideConfirmDeleteModal() {
      const modal = document.getElementById('confirmDeleteModal');
      const backdrop = document.getElementById('confirmDeleteBackdrop');

      modal.classList.remove('visible');

      setTimeout(() => {
        modal.classList.add('hidden');
        backdrop.classList.add('hidden');
      }, 300);
    }

    function confirmDeleteAnnouncement() {
      hideConfirmDeleteModal();

      // แสดง loading modal
      showLoadingModal();

      google.script.run
        .withSuccessHandler(function(result) {
          hideLoadingModal();

          if (result.error) {
            alert('เกิดข้อผิดพลาด: ' + result.error);
            return;
          }

          // อัปเดต UI
          loadInstructorCourses();
        })
        .withFailureHandler(function(error) {
          hideLoadingModal();
          alert('เกิดข้อผิดพลาด: ' + error.message);
        })
        .doManageScoreAnnouncement('delete', deleteScoreSheetName);
    }

    // Score Table Modal Functions
    function showScoreTableModal(scoreSheetName, courseCode, courseName) {
      const modal = document.getElementById('scoreTableModal');
      const backdrop = document.getElementById('scoreTableBackdrop');
      const title = document.getElementById('scoreTableTitle');
      const content = document.getElementById('scoreTableContent');

      title.textContent = `ตารางคะแนน ${courseCode} - ${courseName}`;
      content.innerHTML = '<div class="text-center py-4"><div class="spinner"></div><p class="mt-2">กำลังโหลดข้อมูล...</p></div>';

      backdrop.classList.remove('hidden');
      modal.classList.remove('hidden');

      setTimeout(() => {
        modal.classList.add('visible');
      }, 10);

      // โหลดข้อมูลตารางคะแนน
      google.script.run
        .withSuccessHandler(function(result) {
          if (result && result.scores) {
            displayScoreTable(result.scores, result.headers);
          } else {
            content.innerHTML = '<div class="text-center py-4 text-gray-500">ไม่พบข้อมูลคะแนน</div>';
          }
        })
        .withFailureHandler(function(error) {
          content.innerHTML = '<div class="text-center py-4 text-red-500">เกิดข้อผิดพลาดในการโหลดข้อมูล</div>';
        })
        .getScoreTableData(scoreSheetName);
    }

    function hideScoreTableModal() {
      const modal = document.getElementById('scoreTableModal');
      const backdrop = document.getElementById('scoreTableBackdrop');

      modal.classList.remove('visible');

      setTimeout(() => {
        modal.classList.add('hidden');
        backdrop.classList.add('hidden');
      }, 300);
    }

    function displayScoreTable(scores, headers) {
      const content = document.getElementById('scoreTableContent');

      if (!scores || scores.length === 0) {
        content.innerHTML = '<div class="text-center py-4 text-gray-500">ไม่พบข้อมูลคะแนน</div>';
        return;
      }

      let html = '<div class="overflow-auto max-h-96"><table class="min-w-full divide-y divide-gray-200 text-sm">';

      // Headers
      if (headers && headers.length > 0) {
        html += '<thead class="bg-gray-50"><tr>';
        headers.forEach((header, index) => {
          // Second column (column B) should be sticky
          const stickyClass = index === 1 ? 'sticky left-0 z-10 bg-gray-50' : '';
          html += `<th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky top-0 bg-gray-50 ${stickyClass}">${header}</th>`;
        });
        html += '</tr></thead>';
      }

      // Data rows
      html += '<tbody class="bg-white divide-y divide-gray-200">';
      scores.forEach((row, rowIndex) => {
        const rowBgClass = rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50';
        html += `<tr class="${rowBgClass}">`;

        if (Array.isArray(row)) {
          row.forEach((cell, cellIndex) => {
            // Second column (column B) should be sticky
            const stickyClass = cellIndex === 1 ? `sticky left-0 z-10 ${rowBgClass}` : '';
            html += `<td class="px-3 py-2 whitespace-nowrap text-gray-900 ${stickyClass}">${cell || '-'}</td>`;
          });
        } else {
          // Handle object format
          Object.values(row).forEach((cell, cellIndex) => {
            const stickyClass = cellIndex === 1 ? `sticky left-0 z-10 ${rowBgClass}` : '';
            html += `<td class="px-3 py-2 whitespace-nowrap text-gray-900 ${stickyClass}">${cell || '-'}</td>`;
          });
        }
        html += '</tr>';
      });
      html += '</tbody></table></div>';

      content.innerHTML = html;
    }
  </script>

  <!-- Loading Modal -->
  <div id="loadingBackdrop" class="modal-backdrop hidden">
    <div id="loadingModal" class="fixed inset-0 flex items-center justify-center z-50 hidden">
      <div class="spinner spinner-xl text-white"></div>
    </div>
  </div>

  <!-- Confirm Delete Modal -->
  <div id="confirmDeleteBackdrop" class="modal-backdrop hidden">
    <div id="confirmDeleteModal" class="success-modal hidden">
      <div class="success-modal-content">
        <div class="p-6 text-center">
          <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
            <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
          </div>

          <h3 class="text-lg font-medium text-gray-900 mb-2">
            ยืนยันการลบ
          </h3>

          <p class="text-sm text-gray-500 mb-6">
            คุณต้องการลบประกาศคะแนนนี้หรือไม่? การดำเนินการนี้ไม่สามารถยกเลิกได้
          </p>

          <div class="flex gap-3 justify-center">
            <button
              onclick="hideConfirmDeleteModal()"
              class="bg-gray-300 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-400 transition-colors"
            >
              ยกเลิก
            </button>
            <button
              id="confirmDeleteBtn"
              onclick="confirmDeleteAnnouncement()"
              class="bg-red-500 text-white px-6 py-2 rounded-md hover:bg-red-600 transition-colors"
            >
              ลบ
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Success Modal -->
  <div id="successBackdrop" class="modal-backdrop hidden">
    <div id="successModal" class="success-modal hidden">
      <div class="success-modal-content">
        <div class="p-6 text-center">
          <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
            <i class="fas fa-check text-2xl text-green-600"></i>
          </div>

          <h3 class="text-lg font-medium text-gray-900 mb-6">
            อัปโหลดสำเร็จ
          </h3>

          <div class="text-center">
            <button
              onclick="hideSuccessModal(); showInstructorTab('manage')"
              class="bg-white text-gray-700 border border-gray-300 px-8 py-3 rounded-md hover:bg-gray-50 transition-colors flex items-center mx-auto"
            >
              <i class="fas fa-cog mr-2"></i>ไปหน้าจัดการประกาศ
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Score Table Modal -->
  <div id="scoreTableBackdrop" class="modal-backdrop hidden">
    <div id="scoreTableModal" class="success-modal hidden">
      <div class="success-modal-content w-[95vw] max-w-[75%] lg:max-w-[75%] max-h-[95vh] sm:max-h-[90vh]">
        <div class="flex justify-between items-center p-4 border-b">
          <h3 id="scoreTableTitle" class="text-lg font-medium text-gray-900">ตารางคะแนน</h3>
          <button onclick="hideScoreTableModal()" class="text-gray-400 hover:text-gray-600">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>
        <div id="scoreTableContent" class="p-4 max-h-[80vh] sm:max-h-[75vh] overflow-auto">
          <!-- Table content will be loaded here -->
        </div>
      </div>
    </div>
  </div>
</body>
</html>