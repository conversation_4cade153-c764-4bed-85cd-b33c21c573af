function isRsuAccount(email) {
  return email && email.toLowerCase().endsWith('@rsu.ac.th');
}

function getActiveUserEmail() {
  var email = Session.getActiveUser().getEmail();
  return email;
}

function checkRsuAuth() {
  var email = getActiveUserEmail();
  if (isRsuAccount(email)) {
    // Log successful authentication
    logAccess(email.split('@')[0], email, 'เข้าสู่ระบบ', null);
    return email || "";
  } else {
    // Log failed authentication
    logAccess('unknown', email || 'no email', 'เข้าสู่ระบบไม่สำเร็จ', 'ไม่ใช่บัญชี @rsu.ac.th');
    return "";
  }
} 