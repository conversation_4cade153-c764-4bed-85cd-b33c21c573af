// === Database Structure & Sample Data ===

/**
 * สร้างชีทหลักและเติมข้อมูลตัวอย่าง
 */
function generateSheetsAndSampleData() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();

  // 1. Course list sheet
  var courseSheet = ss.getSheetByName('course_list') || ss.insertSheet('course_list');
  courseSheet.clear();
  courseSheet.appendRow([
    'รหัสวิชา', 'ชื่อวิชา', 'ประเภทสอบ', 'ปีการศึกษา', 'ภาคการศึกษา', 'อาจารย์ผู้ประกาศ', 'วันที่ประกาศ', 'ชื่อชีตคะแนน', 'สถานะ'
  ]);
  courseSheet.appendRow(['DEN101', 'Anatomy', 'midterm', '2567', '1', '<EMAIL>', '2024-07-01', 'DEN101_midterm', 'active']);
  courseSheet.appendRow(['DEN102', 'Physiology', 'final', '2567', '2', '<EMAIL>', '2024-07-02', 'DEN102_final', 'active']);
  courseSheet.appendRow(['DEN201', 'Oral Pathology', 'midterm', '2568', '1', '<EMAIL>', '2025-01-15', 'DEN201_midterm', 'active']);

  // 2. Access log sheet
  var logSheet = ss.getSheetByName('accessLog') || ss.insertSheet('accessLog');
  logSheet.clear();
  logSheet.appendRow(['Timestamp', 'User', 'Email', 'Action', 'Details']);
  logSheet.appendRow([new Date(), 'kunchorn.k', '<EMAIL>', 'เข้าสู่ระบบ', 'ตัวอย่าง log']);

  // 3. Archive sheet
  var archiveSheet = ss.getSheetByName('archive') || ss.insertSheet('archive');
  archiveSheet.clear();
  archiveSheet.appendRow([
    'รหัสวิชา', 'ชื่อวิชา', 'ประเภทสอบ', 'ปีการศึกษา', 'ภาคการศึกษา', 'อาจารย์ผู้ประกาศ', 'วันที่ประกาศ', 'ชื่อชีตคะแนน', 'วันที่ย้ายเข้า archive'
  ]);
  
  // 4. Instructor list sheet
  generateInstructorListSheet();

  // 5. Generate sample score sheets
  generateSampleCourseScoreSheet('DEN101_midterm');
  generateSampleCourseScoreSheet('DEN102_final');
  generateSearchTestData();
}

/**
 * สร้างชีท instructor_list และเติมข้อมูลตัวอย่าง
 */
function generateInstructorListSheet() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var instructorSheet = ss.getSheetByName('instructor_list') || ss.insertSheet('instructor_list');
  
  // ล้างข้อมูลเดิม
  instructorSheet.clear();
  
  // สร้าง header
  instructorSheet.appendRow([
    'Email', 'ชื่อ-นามสกุล', 'ตำแหน่ง', 'สิทธิ์การเข้าถึง', 'วันที่เพิ่ม', 'สถานะ'
  ]);
  
  // เพิ่มข้อมูลตัวอย่าง
  instructorSheet.appendRow([
    '<EMAIL>', 
    'กัญจน์ชร กัญจน์ชร', 
    'อาจารย์', 
    'admin', 
    new Date(), 
    'active'
  ]);
  
  instructorSheet.appendRow([
    '<EMAIL>', 
    'อาจารย์ ตัวอย่าง 1', 
    'อาจารย์', 
    'instructor', 
    new Date(), 
    'active'
  ]);
  
  instructorSheet.appendRow([
    '<EMAIL>', 
    'อาจารย์ ตัวอย่าง 2', 
    'อาจารย์', 
    'instructor', 
    new Date(), 
    'active'
  ]);
  
  // ตั้งค่าการจัดรูปแบบ
  var headerRange = instructorSheet.getRange(1, 1, 1, 6);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#f3f4f6');
  
  // ตั้งค่าความกว้างคอลัมน์
  instructorSheet.setColumnWidth(1, 200); // Email
  instructorSheet.setColumnWidth(2, 150); // ชื่อ-นามสกุล
  instructorSheet.setColumnWidth(3, 100); // ตำแหน่ง
  instructorSheet.setColumnWidth(4, 120); // สิทธิ์การเข้าถึง
  instructorSheet.setColumnWidth(5, 120); // วันที่เพิ่ม
  instructorSheet.setColumnWidth(6, 100); // สถานะ
  
  Logger.log('สร้าง instructor_list sheet สำเร็จ');
}

/**
 * ตรวจสอบว่าผู้ใช้เป็น instructor หรือไม่
 * @param {string} email อีเมลของผู้ใช้
 * @returns {boolean} true ถ้าเป็น instructor
 */
function isInstructor(email) {
  if (!email) return false;
  
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('instructor_list');
  if (!sheet) return false;
  
  var data = sheet.getDataRange().getValues();
  for (var i = 1; i < data.length; i++) {
    var row = data[i];
    var instructorEmail = row[0];
    var status = row[5];
    
    if (instructorEmail.toLowerCase() === email.toLowerCase() && status === 'active') {
      return true;
    }
  }
  
  return false;
}

/**
 * ตรวจสอบว่าผู้ใช้เป็น admin หรือไม่
 * @param {string} email อีเมลของผู้ใช้
 * @returns {boolean} true ถ้าเป็น admin
 */
function isAdmin(email) {
  if (!email) return false;
  
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('instructor_list');
  if (!sheet) return false;
  
  var data = sheet.getDataRange().getValues();
  for (var i = 1; i < data.length; i++) {
    var row = data[i];
    var instructorEmail = row[0];
    var accessLevel = row[3];
    var status = row[5];
    
    if (instructorEmail.toLowerCase() === email.toLowerCase() && 
        accessLevel === 'admin' && 
        status === 'active') {
      return true;
    }
  }
  
  return false;
}

/**
 * ดึงข้อมูล instructor ทั้งหมด
 * @returns {Array} array ของ instructor objects
 */
function getAllInstructors() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('instructor_list');
  if (!sheet) return [];
  
  var data = sheet.getDataRange().getValues();
  var instructors = [];
  
  for (var i = 1; i < data.length; i++) {
    var row = data[i];
    instructors.push({
      email: row[0],
      name: row[1],
      position: row[2],
      accessLevel: row[3],
      addedDate: row[4],
      status: row[5]
    });
  }
  
  return instructors;
}

/**
 * เพิ่ม instructor ใหม่
 * @param {Object} instructor ข้อมูล instructor
 */
function addInstructor(instructor) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('instructor_list');
  if (!sheet) {
    generateInstructorListSheet();
    sheet = ss.getSheetByName('instructor_list');
  }
  
  sheet.appendRow([
    instructor.email,
    instructor.name,
    instructor.position || 'อาจารย์',
    instructor.accessLevel || 'instructor',
    new Date(),
    'active'
  ]);
  
  Logger.log('เพิ่ม instructor: ' + instructor.email);
}

/**
 * อัปเดตสถานะ instructor
 * @param {string} email อีเมลของ instructor
 * @param {string} status สถานะใหม่ (active/inactive)
 */
function updateInstructorStatus(email, status) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('instructor_list');
  if (!sheet) return;
  
  var data = sheet.getDataRange().getValues();
  for (var i = 1; i < data.length; i++) {
    var row = data[i];
    if (row[0].toLowerCase() === email.toLowerCase()) {
      sheet.getRange(i + 1, 6).setValue(status);
      Logger.log('อัปเดตสถานะ instructor: ' + email + ' เป็น ' + status);
      break;
    }
  }
}

/**
 * ดึงรายวิชาที่อาจารย์เป็นผู้ประกาศ
 * @param {string} instructorEmail อีเมลของอาจารย์
 * @returns {Array} รายวิชาที่อาจารย์เป็นผู้ประกาศ
 */
function getInstructorCourses(instructorEmail) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var courseSheet = ss.getSheetByName('course_list');
  if (!courseSheet) return [];

  var data = courseSheet.getDataRange().getValues();
  var courses = [];

  for (var i = 1; i < data.length; i++) {
    var row = data[i];
    var teacherEmail = row[5];

    if (teacherEmail.toLowerCase() === instructorEmail.toLowerCase()) {
      courses.push({
        code: row[0],
        name: row[1],
        examType: row[2],
        year: row[3],
        term: row[4],
        teacher: row[5],
        announceDate: (row[6] instanceof Date) ? row[6].toISOString() : String(row[6]),
        scoreSheetName: row[7],
        isActive: row[8] === 'active' // Column I - status
      });
    }
  }

  return courses;
}

/**
 * ดึงรายวิชาทั้งหมด (สำหรับ admin)
 * @returns {Array} รายวิชาทั้งหมด
 */
function getAllCourses() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var courseSheet = ss.getSheetByName('course_list');
  if (!courseSheet) return [];

  var data = courseSheet.getDataRange().getValues();
  var courses = [];

  for (var i = 1; i < data.length; i++) {
    var row = data[i];
    courses.push({
      code: row[0],
      name: row[1],
      examType: row[2],
      year: row[3],
      term: row[4],
      teacher: row[5],
      announceDate: (row[6] instanceof Date) ? row[6].toISOString() : String(row[6]),
      scoreSheetName: row[7],
      isActive: row[8] === 'active' // Column I - status
    });
  }

  return courses;
}




/**
 * สร้าง archive sheet ใน spreadsheet แยก (archiveLog)
 */
function setupArchiveSheet() {
  var archiveSS = SpreadsheetApp.openById('1iN2pNyXKvzoXb-lxAiR2aHyZ-LQbwDugZIyiC51CEr0');
  var archiveSheet = archiveSS.getSheetByName('archiveLog') || archiveSS.insertSheet('archiveLog');
  archiveSheet.clear();
  archiveSheet.appendRow([
    'รหัสวิชา', 'ชื่อวิชา', 'ประเภทสอบ', 'ปีการศึกษา', 'ภาคการศึกษา', 'อาจารย์ผู้ประกาศ', 'วันที่ประกาศ', 'ชื่อชีตคะแนน', 'วันที่ย้ายเข้า archive'
  ]);
}

/**
 * สร้างชีทคะแนนรายวิชาตัวอย่าง
 * @param {string} sheetName ชื่อชีทคะแนน เช่น DEN101_midterm
 */
function generateSampleCourseScoreSheet(sheetName) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var scoreSheet = ss.getSheetByName(sheetName) || ss.insertSheet(sheetName);
  scoreSheet.clear();
  scoreSheet.appendRow([
    'student ID', 'student name', 'student email', 'score', 'full score'
  ]);
  scoreSheet.appendRow(['7000001', 'สมชาย ใจดี', '<EMAIL>', 78, 100]);
  scoreSheet.appendRow(['7000002', 'สมหญิง เก่งมาก', '<EMAIL>', 85, 100]);
  scoreSheet.appendRow(['7000003', 'สมปอง สู้ๆ', '<EMAIL>', 92, 100]);
}

/**
 * สร้างข้อมูลตัวอย่างสำหรับการทดสอบระบบค้นหา
 */
function generateSearchTestData() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();

  // สร้างชีตคะแนนสำหรับ DEN201_midterm
  var scoreSheet = ss.getSheetByName('DEN201_midterm') || ss.insertSheet('DEN201_midterm');
  scoreSheet.clear();

  // Headers
  scoreSheet.appendRow([
    'student ID', 'student name', 'student email', 'score Part A', 'full score Part A',
    'score Part B', 'full score Part B', 'Total Score', 'Total Full Score',
    'Mean', 'SD', 'Min', 'Max', 'Rank'
  ]);

  // Sample data with statistics - ใช้ชื่อแบบสุ่ม
  var randomNames = getRandomNames(10);
  var sampleData = [
    ['6811101', randomNames[0], '<EMAIL>', 35, 50, 40, 50, 75, 100, 82.5, 8.66, 65, 95, '8_10'],
    ['6811102', randomNames[1], '<EMAIL>', 42, 50, 48, 50, 90, 100, 82.5, 8.66, 65, 95, '2_10'],
    ['6811103', randomNames[2], '<EMAIL>', 45, 50, 50, 50, 95, 100, 82.5, 8.66, 65, 95, '1_10'],
    ['6811104', randomNames[3], '<EMAIL>', 38, 50, 42, 50, 80, 100, 82.5, 8.66, 65, 95, '5_10'],
    ['6811105', randomNames[4], '<EMAIL>', 30, 50, 35, 50, 65, 100, 82.5, 8.66, 65, 95, '10_10'],
    ['6811106', randomNames[5], '<EMAIL>', 40, 50, 45, 50, 85, 100, 82.5, 8.66, 65, 95, '3_10'],
    ['6811107', randomNames[6], '<EMAIL>', 32, 50, 38, 50, 70, 100, 82.5, 8.66, 65, 95, '9_10'],
    ['6811108', randomNames[7], '<EMAIL>', 39, 50, 44, 50, 83, 100, 82.5, 8.66, 65, 95, '4_10'],
    ['6811109', randomNames[8], '<EMAIL>', 36, 50, 41, 50, 77, 100, 82.5, 8.66, 65, 95, '7_10'],
    ['6811110', randomNames[9], '<EMAIL>', 37, 50, 43, 50, 80, 100, 82.5, 8.66, 65, 95, '6_10']
  ];

  // เพิ่มข้อมูลลงในชีต
  for (var i = 0; i < sampleData.length; i++) {
    scoreSheet.appendRow(sampleData[i]);
  }

  Logger.log('สร้างข้อมูลตัวอย่างสำหรับการทดสอบระบบค้นหาเสร็จสิ้น');
}

/**
 * ดึงข้อมูลคะแนนจากชีทคะแนนรายวิชา
 * @param {string} sheetName
 * @returns {Array} array of score objects
 */
function getSampleCourseScores(sheetName) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName(sheetName);
  if (!sheet) return [];
  var data = sheet.getDataRange().getValues();
  return data.slice(1).map(function(row) {
    return {
      studentId: row[0],
      studentName: row[1],
      studentEmail: row[2],
      score: row[3],
      fullScore: row[4]
    };
  });
}

// === Data Validation ===
function isValidStudentId(id) {
  return /^\d{7}$/.test(id);
}

function isValidEmail(email) {
  return /@rsu\.ac\.th$/.test(email);
}

// === Database Helper Functions (CRUD) ===
function addCourse(course) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('course_list');
  sheet.appendRow([
    course.code, course.name, course.examType, course.year, course.term, course.teacher, course.announceDate, course.scoreSheetName, course.isActive || 'active'
  ]);
}

function getCourses() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('course_list');
  var data = sheet.getDataRange().getValues();
  return data.slice(1).map(function(row) {
    return {
      code: row[0], name: row[1], examType: row[2], year: row[3], term: row[4], teacher: row[5], announceDate: row[6], scoreSheetName: row[7], isActive: row[8] === 'active'
    };
  });
}

function logAccess(user, email, action, details) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('accessLog');
  sheet.appendRow([new Date(), user, email, action, details]);
}

/**
 * ดึงรายวิชาทั้งหมดที่มีคะแนนของ email นี้
 * @param {string} email
 * @returns {Array} รายวิชาที่มีคะแนนของ email นี้
 */
function getStudentCourses() {
  var email = Session.getActiveUser().getEmail();
  if (!email || typeof email !== 'string' || email.trim() === '') {
    Logger.log('ERROR: No active user email');
    return [];
  }
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var courseSheet = ss.getSheetByName('course_list');
  var courses = courseSheet.getDataRange().getValues().slice(1);
  var result = [];
  Logger.log('=== DEBUG getStudentCourses ===');
  Logger.log('Target email: ' + email);
  courses.forEach(function(row) {
    var sheetName = row[7];
    var courseStatus = row[8]; // Column I - status

    // ตรวจสอบสถานะ - ถ้าเป็น inactive ไม่แสดงรายวิชานี้
    if (courseStatus === 'inactive') {
      Logger.log('Skipping inactive course: ' + sheetName);
      return;
    }

    Logger.log('Checking sheet: ' + sheetName);
    var scoreSheet = ss.getSheetByName(sheetName);
    if (!scoreSheet) {
      Logger.log('Sheet not found: ' + sheetName);
      return;
    }
    var data = scoreSheet.getDataRange().getValues();
    for (var i = 1; i < data.length; i++) {
      Logger.log('Row ' + i + ' email: ' + data[i][2]);
      if ((data[i][2] || '').toLowerCase() === email.toLowerCase()) {
        Logger.log('MATCH: ' + data[i][2]);
        result.push({
          code: row[0],
          name: row[1],
          examType: row[2],
          year: row[3],
          term: row[4],
          teacher: row[5],
          announceDate: (row[6] instanceof Date) ? row[6].toISOString() : String(row[6]),
          scoreSheetName: row[7],
          isActive: courseStatus === 'active'
        });
        break;
      }
    }
  });
  Logger.log('Result count: ' + result.length);
  return result;
}

/**
 * ดึงคะแนนและ stat ของนักศึกษาคนนี้ในรายวิชานั้น ๆ
 * @param {string} email
 * @param {string} sheetName
 * @returns {Object} { scores: [...], stats: {...} }
 */
function getStudentCourseScores(sheetName) {
  var email = Session.getActiveUser().getEmail();
  if (!email || typeof email !== 'string' || email.trim() === '') {
    Logger.log('ERROR: No active user email');
    return { scores: [], stats: {} };
  }
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName(sheetName);
  if (!sheet) return { scores: [], stats: {} };
  var data = sheet.getDataRange().getValues();
  var headers = data[0];
  // หา index ของ section ทั้งหมด
  var sectionIndexes = [];
  for (var i = 3; i < headers.length - 1; i++) {
    var h1 = (headers[i] || '').toString();
    var h2 = (headers[i+1] || '').toString();
    if (h1.includes('score') && h2.includes('full score')) {
      // หาชื่อ section เช่น "Section A" จากชื่อ header
      var sectionName = h1.replace('score', '').trim() || 'Section ' + (sectionIndexes.length + 1);
      sectionIndexes.push({
        section: sectionName,
        scoreIdx: i,
        fullScoreIdx: i+1
      });
      i++; // skip next (full score)
    }
  }
  // ดึงสถิติที่คำนวณไว้แล้วจากชีต
  var studentRows = data.slice(1).filter(function(row) { return (row[2] || '').toLowerCase() === email.toLowerCase(); });
  var stats = {};

  if (studentRows.length > 0) {
    var studentRow = studentRows[0];

    // หาคอลัมน์สถิติ (Mean, SD, Min, Max, Rank)
    var meanCol = -1, sdCol = -1, minCol = -1, maxCol = -1, rankCol = -1;

    for (var c = 0; c < headers.length; c++) {
      var header = (headers[c] || '').toString().toLowerCase();
      if (header === 'mean') meanCol = c;
      else if (header === 'sd') sdCol = c;
      else if (header === 'min') minCol = c;
      else if (header === 'max') maxCol = c;
      else if (header === 'rank') rankCol = c;
    }

    Logger.log('Statistics columns found - Mean: ' + meanCol + ', SD: ' + sdCol + ', Min: ' + minCol + ', Max: ' + maxCol + ', Rank: ' + rankCol);

    // อ่านค่าสถิติจากชีต
    if (meanCol !== -1) stats.mean = parseFloat(studentRow[meanCol]) || 0;
    if (sdCol !== -1) stats.sd = parseFloat(studentRow[sdCol]) || 0;
    if (minCol !== -1) stats.min = parseFloat(studentRow[minCol]) || 0;
    if (maxCol !== -1) stats.max = parseFloat(studentRow[maxCol]) || 0;
    if (rankCol !== -1) {
      var rankValue = studentRow[rankCol] || '';
      Logger.log('Raw rank value: ' + rankValue + ' (type: ' + typeof rankValue + ')');

      if (typeof rankValue === 'string') {
        // Handle both old format (2/3) and new format (2_3)
        if (rankValue.includes('/')) {
          var rankParts = rankValue.split('/');
          stats.rank = parseInt(rankParts[0]) || 1;
          stats.total = parseInt(rankParts[1]) || 1;
        } else if (rankValue.includes('_')) {
          var rankParts = rankValue.split('_');
          stats.rank = parseInt(rankParts[0]) || 1;
          stats.total = parseInt(rankParts[1]) || 1;
        }
      } else if (typeof rankValue === 'number') {
        stats.rank = rankValue;
        stats.total = data.length - 1; // exclude header row
      }

      Logger.log('Parsed rank: ' + stats.rank + '/' + stats.total);
    }

    // คำนวณ percentile จาก rank
    if (stats.rank && stats.total) {
      stats.percentile = Math.round((stats.total - stats.rank) / stats.total * 100);
    }
  }

  // ถ้าไม่มีสถิติในชีต ให้คำนวณแบบเดิม (fallback)
  if (!stats.mean) {
    var allScores = [];
    for (var r = 1; r < data.length; r++) {
      var v = data[r][data[0].length - 2]; // col รองสุดท้าย = คะแนนที่ได้
      if (typeof v === 'number' || (!isNaN(parseFloat(v)) && isFinite(v))) {
        allScores.push(Number(v));
      }
    }
    stats = calculateStats(allScores);

    // หา rank ของนักศึกษาคนนี้
    var studentScore = null;
    if (studentRows.length > 0) {
      var v = studentRows[0][data[0].length - 2];
      if (typeof v === 'number' || (!isNaN(parseFloat(v)) && isFinite(v))) {
        studentScore = Number(v);
      }
    }
    if (studentScore !== null) {
      var sorted = allScores.slice().sort(function(a, b) { return b - a; });
      var rank = sorted.indexOf(studentScore) + 1;
      var percentile = Math.round((sorted.length - rank) / sorted.length * 100);
      stats.rank = rank;
      stats.percentile = percentile;
      stats.total = stats.total || sorted.length;
    }
  }
  return {
    scores: studentRows.map(function(row) {
      var sections = sectionIndexes.map(function(sec) {
        return {
          section: sec.section,
          score: row[sec.scoreIdx],
          fullScore: row[sec.fullScoreIdx]
        };
      });
      return {
        studentId: row[0],
        studentName: row[1],
        studentEmail: row[2],
        sections: sections
      };
    }),
    stats: stats,
    sectionNames: sectionIndexes.map(function(sec) { return sec.section; })
  };
}

/**
 * คำนวณ Mean, SD, Min, Max
 * @param {Array} scores (number[])
 * @returns {Object}
 */
function calculateStats(scores) {
  if (!scores || scores.length === 0) return {};
  var n = scores.length;
  var mean = scores.reduce(function(a, b) { return a + Number(b); }, 0) / n;
  var sd = Math.sqrt(scores.map(function(x) { return Math.pow(Number(x) - mean, 2); }).reduce(function(a, b) { return a + b; }, 0) / n);
  var min = Math.min.apply(null, scores);
  var max = Math.max.apply(null, scores);
  return { mean: mean, sd: sd, min: min, max: max, count: n, total: n };
}

/**
 * คำนวณ rank สำหรับคะแนนที่เรียงลำดับแล้ว
 * @param {Array} sortedScores คะแนนที่เรียงจากมากไปน้อยแล้ว
 * @returns {Array} rank สำหรับแต่ละคะแนน
 */
function calculateRanks(sortedScores) {
  var ranks = [];
  var currentRank = 1;

  for (var i = 0; i < sortedScores.length; i++) {
    if (i > 0 && sortedScores[i] !== sortedScores[i - 1]) {
      currentRank = i + 1;
    }
    ranks.push(currentRank);
  }

  return ranks;
}

/**
 * หา rank ของคะแนนเฉพาะ
 * @param {number} score คะแนนที่ต้องการหา rank
 * @param {Array} sortedScores คะแนนที่เรียงจากมากไปน้อยแล้ว
 * @param {Array} ranks rank ที่คำนวณแล้ว
 * @returns {number} rank ของคะแนนนั้น
 */
function getRankForScore(score, sortedScores, ranks) {
  var index = sortedScores.indexOf(score);
  return index !== -1 ? ranks[index] : sortedScores.length;
}

/**
 * สร้าง template สำหรับกรอกคะแนน
 * @param {Object} templateConfig การกำหนดค่า template (type, sections)
 * @returns {Object} ข้อมูล template
 */
function generateScoreTemplate(templateConfig) {
  // ถ้าไม่มี templateConfig ให้ใช้ค่าเริ่มต้น
  if (!templateConfig) {
    templateConfig = { type: 'single' };
  }

  // สร้าง template ตามประเภท
  if (templateConfig.type === 'multi' && templateConfig.sections && templateConfig.sections.length > 0) {
    return generateMultiSectionTemplate(templateConfig.sections);
  } else if (templateConfig.type === 'custom' && templateConfig.columns && templateConfig.columns.length > 0) {
    return generateCustomDataTemplate(templateConfig.columns);
  } else {
    // Single section template (default)
    var names = getRandomNames(2);
    var template = {
      headers: ['student ID', 'student name', 'student email', 'score', 'full score'],
      sampleData: [
        ['6811101', names[0], '<EMAIL>', Math.floor(Math.random() * 25) + 70, 100], // 70-94
        ['6811102', names[1], '<EMAIL>', Math.floor(Math.random() * 25) + 70, 100]  // 70-94
      ],
      instructions: [
        '1. กรอกข้อมูลนักศึกษาในคอลัมน์ student ID, student name, student email',
        '2. กรอกคะแนนที่ได้และคะแนนเต็มในคอลัมน์ที่ 4 และ 5',
        '3. ตรวจสอบให้แน่ใจว่า student ID เป็นเลข 7 หลัก และ email เป็น @rsu.ac.th',
        '4. บันทึกไฟล์เป็น CSV (UTF-8) เพื่อรองรับภาษาไทย',
        '5. ระบบจะคำนวณสถิติจากคะแนนที่กรอกมา'
      ]
    };

    return template;
  }
}

/**
 * สุ่มชื่อจากรายชื่อที่กำหนด
 * @param {number} count จำนวนชื่อที่ต้องการ
 * @returns {Array} array ของชื่อที่สุ่มได้
 */
function getRandomNames(count) {
  var nameList = [
    'พิชญ์พิสิฐฎ์เสฎ', 'ธนญญ์นภสสร์', 'ณัฏฐเศรษฐาวิชญ์', 'กุญจ์สิริลัญฉกร',
    'อคฐิษรัชธภดา', 'พจชรดลญา', 'มัฎศิญากรเกศญ์', 'มนมนัสกรณ์',
    'ลีฬฆตวรรณ', 'นภัสณชพร', 'นัฐตร์ฐานันทตร์', 'พิธิจิตตราพันธ์',
    'ภีมจรัษพงษ์', 'มัฌฌณิฌาศ์', 'ศรชฏางค์เศรษฐ์', 'สกธินชารินทร์พธา'
  ];

  var selectedNames = [];

  for (var i = 0; i < Math.min(count, nameList.length); i++) {
    var firstName, lastName;
    var attempts = 0;
    var maxAttempts = 50; // ป้องกัน infinite loop

    do {
      // สุ่มชื่อและนามสกุล
      firstName = nameList[Math.floor(Math.random() * nameList.length)];
      lastName = nameList[Math.floor(Math.random() * nameList.length)];
      attempts++;
    } while (firstName === lastName && attempts < maxAttempts);

    // ถ้าไม่สามารถหาชื่อที่ไม่ซ้ำได้ ให้ใช้วิธี fallback
    if (firstName === lastName) {
      var firstIndex = Math.floor(Math.random() * nameList.length);
      var secondIndex = (firstIndex + 1) % nameList.length;
      firstName = nameList[firstIndex];
      lastName = nameList[secondIndex];
    }

    selectedNames.push(firstName + ' ' + lastName);
  }

  return selectedNames;
}

/**
 * คำนวณคะแนนเต็มแต่ละ section ให้รวมเป็น 100
 * @param {number} numSections จำนวน section
 * @returns {Array} array ของคะแนนเต็มแต่ละ section
 */
function calculateSectionScores(numSections) {
  var baseScore = Math.floor(100 / numSections);
  var remainder = 100 % numSections;
  var scores = [];

  for (var i = 0; i < numSections; i++) {
    scores.push(baseScore + (i < remainder ? 1 : 0));
  }

  return scores;
}

/**
 * สร้าง template สำหรับหลาย section (ผู้ใช้ต้องกรอกคะแนนรวมเอง)
 * @param {Array} sections array ของชื่อ section
 * @returns {Object} ข้อมูล template
 */
function generateMultiSectionTemplate(sections) {
  var headers = ['student ID', 'student name', 'student email'];

  // สุ่มชื่อสำหรับตัวอย่าง
  var names = getRandomNames(2);

  var sampleData = [
    ['6811101', names[0], '<EMAIL>'],
    ['6811102', names[1], '<EMAIL>']
  ];

  // เพิ่ม headers สำหรับแต่ละ section
  for (var i = 0; i < sections.length; i++) {
    var sectionName = sections[i] || ('Section ' + String.fromCharCode(65 + i)); // A, B, C, ...
    headers.push('score ' + sectionName);
    headers.push('full score ' + sectionName);
  }

  // เพิ่มคอลัมน์คะแนนรวม
  headers.push('Total Score');
  headers.push('Total Full Score');

  // คำนวณคะแนนเต็มแต่ละ section ให้รวมเป็น 100
  var sectionScores = calculateSectionScores(sections.length);

  // เพิ่มข้อมูลตัวอย่าง
  for (var i = 0; i < sampleData.length; i++) {
    var totalScore = 0;
    var totalFullScore = 0;

    // เพิ่มคะแนนสำหรับแต่ละ section
    for (var j = 0; j < sections.length; j++) {
      var fullScore = sectionScores[j];
      // สร้างคะแนนที่แตกต่างกันสำหรับแต่ละคน
      var minPercent = 0.70 + (i * 0.05); // คนแรก 70%, คนที่สอง 75%
      var maxPercent = 0.90 + (i * 0.05); // คนแรก 90%, คนที่สอง 95%
      var score = Math.floor(fullScore * (minPercent + Math.random() * (maxPercent - minPercent)));

      sampleData[i].push(score);
      sampleData[i].push(fullScore);

      totalScore += score;
      totalFullScore += fullScore;
    }

    // เพิ่มคะแนนรวม
    sampleData[i].push(totalScore);
    sampleData[i].push(totalFullScore);
  }

  return {
    headers: headers,
    sampleData: sampleData,
    instructions: [
      '1. กรอกข้อมูลนักศึกษาในคอลัมน์ student ID, student name, student email',
      '2. กรอกคะแนนสำหรับแต่ละ section ในคอลัมน์คู่ (คะแนนที่ได้ และ คะแนนเต็ม)',
      '3. **สำคัญ:** กรอกคะแนนรวมและคะแนนเต็มรวมในคอลัมน์สุดท้ายด้วยตัวเอง',
      '4. ตรวจสอบให้แน่ใจว่า student ID เป็นเลข 7 หลัก และ email เป็น @rsu.ac.th',
      '5. บันทึกไฟล์เป็น CSV (UTF-8) เพื่อรองรับภาษาไทย',
      '6. ระบบจะคำนวณสถิติจากคะแนนรวมเท่านั้น'
    ]
  };
}

/**
 * สร้าง template สำหรับข้อมูลกำหนดเอง (Custom Data)
 * @param {Array} columns array ของชื่อคอลัมน์
 * @returns {Object} ข้อมูล template
 */
function generateCustomDataTemplate(columns) {
  var headers = ['student ID', 'student name', 'student email'];

  // สุ่มชื่อสำหรับตัวอย่าง
  var names = getRandomNames(2);

  var sampleData = [
    ['6811101', names[0], '<EMAIL>'],
    ['6811102', names[1], '<EMAIL>']
  ];

  // เพิ่ม headers สำหรับแต่ละคอลัมน์
  for (var i = 0; i < columns.length; i++) {
    headers.push(columns[i]);
  }

  // เพิ่มข้อมูลตัวอย่าง
  var sampleValues = [
    ['ผ่าน', 'ไม่ผ่าน', 'รอพิจารณา', 'ดีเยี่ยม', 'ปกติ'],
    ['', 'ติดต่อ อ.ผู้รับผิดชอบ', 'ส่งงานเพิ่มเติม', 'ยินดีด้วย', 'หมายเหตุเพิ่มเติม'],
    ['Active', 'Inactive', 'Pending', 'Completed', 'In Progress'],
    ['ข้อมูลเพิ่มเติม 1', 'ข้อมูลเพิ่มเติม 2', 'รายละเอียด', 'หมายเหตุ', 'อื่นๆ'],
    ['A', 'B', 'C', 'D', 'F']
  ];

  for (var i = 0; i < sampleData.length; i++) {
    for (var j = 0; j < columns.length; j++) {
      // สุ่มเลือกค่าตัวอย่างที่เหมาะสม
      var valueSet = sampleValues[j] || sampleValues[0];
      var randomValue = valueSet[Math.floor(Math.random() * valueSet.length)];
      sampleData[i].push(randomValue);
    }
  }

  return {
    headers: headers,
    sampleData: sampleData,
    instructions: [
      '1. กรอกข้อมูลนักศึกษาในคอลัมน์ student ID, student name, student email',
      '2. กรอกข้อมูลในแต่ละคอลัมน์ตามที่กำหนด (เช่น ผ่าน/ไม่ผ่าน, หมายเหตุ)',
      '3. ไม่จำกัดรูปแบบข้อมูล สามารถใส่ข้อความ ตัวเลข หรือสัญลักษณ์ใดก็ได้',
      '4. ระบบจะไม่คำนวณสถิติสำหรับข้อมูลประเภทนี้',
      '5. ตรวจสอบให้แน่ใจว่า student ID เป็นเลข 7 หลัก และ email เป็น @rsu.ac.th'
    ]
  };
}

/**
 * สร้าง CSV content สำหรับ template พร้อม UTF-8 BOM
 * @param {Object} templateConfig การกำหนดค่า template (type, sections)
 * @returns {string} CSV content
 */
function generateCSVTemplate(templateConfig) {
  var template = generateScoreTemplate(templateConfig);
  var csvContent = '\uFEFF'; // UTF-8 BOM

  // Headers
  csvContent += template.headers.join(',') + '\n';

  // Sample data
  template.sampleData.forEach(function(row) {
    csvContent += row.join(',') + '\n';
  });

  return csvContent;
}

/**
 * ตรวจสอบประเภท template จากชื่อไฟล์และ headers
 * @param {string} fileName ชื่อไฟล์
 * @param {Array} headers array ของ headers
 * @returns {string} ประเภท template ('custom' หรือ 'score')
 */
function detectTemplateType(fileName, headers) {
  // 1. ตรวจสอบชื่อไฟล์ก่อน
  if (fileName && fileName.toLowerCase().startsWith('custom_data_')) {
    Logger.log('Detected custom data from filename: ' + fileName);
    return 'custom';
  }

  // 2. ตรวจสอบ header pattern
  var hasScorePattern = false;
  for (var i = 3; i < headers.length - 1; i++) {
    var h1 = (headers[i] || '').toString().toLowerCase();
    var h2 = (headers[i+1] || '').toString().toLowerCase();

    if ((h1.includes('score') && h2.includes('full')) ||
        (h1.includes('คะแนน') && h2.includes('เต็ม'))) {
      hasScorePattern = true;
      break;
    }
  }

  // 3. ส่งผลลัพธ์
  var templateType = hasScorePattern ? 'score' : 'custom';
  Logger.log('Template type detected: ' + templateType + ' (hasScorePattern: ' + hasScorePattern + ')');
  return templateType;
}

/**
 * ประมวลผลการอัปโหลดคะแนน
 * @param {string} instructorEmail อีเมลของอาจารย์
 * @param {Object} uploadData ข้อมูลการอัปโหลด
 * @returns {Object} ผลการประมวลผล
 */
function processScoreUpload(instructorEmail, uploadData) {
  var scoreSheet = null;
  var scoreSheetName = null;

  try {
    // ตรวจสอบข้อมูลที่จำเป็น
    if (!uploadData.courseCode || !uploadData.courseName || !uploadData.examType ||
        !uploadData.year || !uploadData.term || !uploadData.scoreData) {
      return { error: 'ข้อมูลไม่ครบถ้วน' };
    }

    // ทำความสะอาดข้อมูล
    var cleanedResult = cleanScoreData(uploadData.scoreData);
    if (cleanedResult.error) {
      return { error: 'ข้อมูลไม่ถูกต้อง: ' + cleanedResult.error };
    }

    var cleanedData = cleanedResult.data;

    // Debug: แสดงข้อมูลที่ทำความสะอาดแล้ว
    Logger.log('DEBUG: Cleaned data = ' + JSON.stringify(cleanedData));

    // ตรวจสอบประเภท template
    var fileName = uploadData.fileName || 'unknown.csv';
    var headers = cleanedData.length > 0 ? cleanedData[0] : [];
    var templateType = detectTemplateType(fileName, headers);
    Logger.log('Template type detected: ' + templateType);

    // ตรวจสอบรูปแบบข้อมูล (เฉพาะสำหรับ score data)
    if (templateType === 'score') {
      var validation = validateScoreData(cleanedData);
      if (!validation.isValid) {
        return { error: 'ข้อมูลไม่ถูกต้อง: ' + validation.errors.join(', ') };
      }
    } else {
      Logger.log('Skipping score validation for custom data template');
    }

    // สร้างชื่อชีตคะแนนที่ไม่ซ้ำ
    var baseSheetName = uploadData.courseCode + '_' + uploadData.examType + '_' + uploadData.year + '_' + uploadData.term;
    scoreSheetName = generateUniqueSheetName(baseSheetName);

    // สร้างชีตคะแนนใหม่
    var ss = SpreadsheetApp.getActiveSpreadsheet();
    scoreSheet = ss.insertSheet(scoreSheetName);

    // เขียนข้อมูลคะแนน
    var headers = cleanedData[0];
    var numCols = headers.length;

    Logger.log('Writing headers: ' + JSON.stringify(headers) + ', numCols: ' + numCols);

    // เขียน headers
    scoreSheet.getRange(1, 1, 1, numCols).setValues([headers]);

    // เขียนข้อมูลคะแนน
    if (cleanedData.length > 1) {
      Logger.log('Writing score data: ' + (cleanedData.length - 1) + ' rows, ' + numCols + ' cols');
      scoreSheet.getRange(2, 1, cleanedData.length - 1, numCols)
        .setValues(cleanedData.slice(1));
    }

    // เพิ่มข้อมูลลงใน course_list ก่อนเพิ่มสถิติ
    var courseData = [
      uploadData.courseCode,
      uploadData.courseName,
      uploadData.examType,
      uploadData.year,
      uploadData.term,
      instructorEmail,
      new Date(),
      scoreSheetName,
      uploadData.isActive !== false ? 'active' : 'inactive' // Column I - default to active
    ];

    var courseSheet = ss.getSheetByName('course_list');
    if (!courseSheet) {
      Logger.log('ERROR: course_list sheet not found');
      return { error: 'ไม่พบชีต course_list' };
    }

    Logger.log('Adding course to course_list: ' + JSON.stringify(courseData));
    courseSheet.appendRow(courseData);

    // คำนวณและเพิ่มสถิติ (ทำหลังจากเพิ่มข้อมูลใน course_list แล้ว)
    try {
      addStatisticsToSheet(scoreSheet, headers);
      Logger.log('Statistics added successfully');
    } catch (statsError) {
      Logger.log('Statistics error (non-critical): ' + statsError.toString());
      // ไม่ return error เพราะข้อมูลหลักอัปโหลดสำเร็จแล้ว
    }

    return {
      success: true,
      message: 'อัปโหลดคะแนนสำเร็จ',
      scoreSheetName: scoreSheetName
    };

  } catch (e) {
    Logger.log('processScoreUpload ERROR: ' + e.toString());

    // ลบชีตที่สร้างไว้ถ้าเกิดข้อผิดพลาด
    if (scoreSheet && scoreSheetName) {
      try {
        var ss = SpreadsheetApp.getActiveSpreadsheet();
        var sheetToDelete = ss.getSheetByName(scoreSheetName);
        if (sheetToDelete) {
          ss.deleteSheet(sheetToDelete);
          Logger.log('Deleted failed sheet: ' + scoreSheetName);
        }
      } catch (deleteError) {
        Logger.log('Error deleting failed sheet: ' + deleteError.toString());
      }
    }

    return { error: 'เกิดข้อผิดพลาด: ' + e.toString() };
  }
}

/**
 * ตรวจสอบความถูกต้องของข้อมูลคะแนน
 * @param {Array} scoreData ข้อมูลคะแนน
 * @returns {Object} ผลการตรวจสอบ
 */
function validateScoreData(scoreData) {
  var errors = [];
  
  if (!scoreData || scoreData.length < 2) {
    errors.push('ไม่มีข้อมูลคะแนน');
    return { isValid: false, errors: errors };
  }
  
  var headers = scoreData[0];
  var numCols = headers.length;
  
  // ตรวจสอบจำนวนคอลัมน์ขั้นต่ำ
  if (numCols < 5) {
    errors.push('ข้อมูลต้องมีอย่างน้อย 5 คอลัมน์ (student ID, student name, student email, score, full score)');
    return { isValid: false, errors: errors };
  }
  
  // ตรวจสอบข้อมูลในแต่ละแถว
  for (var r = 1; r < scoreData.length; r++) {
    var row = scoreData[r];
    var rowNum = r + 1;
    
    // ตรวจสอบ student ID (คอลัมน์ที่ 1)
    var studentId = String(row[0] || '');
    if (!isValidStudentId(studentId)) {
      errors.push('แถว ' + rowNum + ': Student ID ไม่ถูกต้อง (ต้องเป็นเลข 7 หลัก)');
    }
    
    // ตรวจสอบ email (คอลัมน์ที่ 3)
    var email = String(row[2] || '');
    if (!isValidEmail(email)) {
      errors.push('แถว ' + rowNum + ': Email ไม่ถูกต้อง (ต้องเป็น @rsu.ac.th)');
    }
    
    // ตรวจสอบคะแนน (เริ่มจากคอลัมน์ที่ 4)
    for (var c = 3; c < numCols; c++) {
      var score = row[c];
      if (c % 2 === 1) { // score (คอลัมน์คี่)
        if (isNaN(parseFloat(score)) || parseFloat(score) < 0) {
          errors.push('แถว ' + rowNum + ' คอลัมน์ ' + (c + 1) + ': คะแนนไม่ถูกต้อง');
        }
      } else { // full score (คอลัมน์คู่)
        if (isNaN(parseFloat(score)) || parseFloat(score) <= 0) {
          errors.push('แถว ' + rowNum + ' คอลัมน์ ' + (c + 1) + ': คะแนนเต็มไม่ถูกต้อง');
        }
      }
    }
  }
  
  return { isValid: errors.length === 0, errors: errors };
}

/**
 * เพิ่มสถิติลงในชีตคะแนน
 * @param {Sheet} scoreSheet ชีตคะแนน
 * @param {Array} headers headers ของชีต
 */
function addStatisticsToSheet(scoreSheet, headers) {
  try {
    var data = scoreSheet.getDataRange().getValues();
    var numCols = headers.length;

    // หา index ของคอลัมน์คะแนนโดยใช้ column index
    var scoreColIndex = 3; // คอลัมน์ที่ 4 (index 3) สำหรับ single section

    // ตรวจสอบว่ามีคอลัมน์คะแนนหรือไม่
    if (numCols <= scoreColIndex) {
      Logger.log('ERROR: ไม่มีคอลัมน์คะแนนเพียงพอ');
      return;
    }

    // อ่านคะแนนรวมจากคอลัมน์ที่ผู้ใช้กรอกมา (ไม่คำนวณอัตโนมัติ)
    var totalScores = [];
    var addedStatCols = false;

    for (var r = 1; r < data.length; r++) {
      var row = data[r];
      var score = 0;

      // กรณีมีหลาย section - อ่านคะแนนรวมจากคอลัมน์สุดท้าย
      if (numCols > 5) {
        // อ่านคะแนนรวมจากคอลัมน์ Total Score (คอลัมน์ที่ 2 จากท้าย)
        score = parseFloat(row[numCols - 2]) || 0; // คอลัมน์ Total Score
      } else {
        // กรณีมีแค่คอลัมน์เดียว
        score = parseFloat(row[scoreColIndex]) || 0;
      }

      totalScores.push(score);

      // ไม่เพิ่มคอลัมน์ Total Score อีกต่อไป - ใช้คะแนนที่ผู้ใช้กรอกมา
    }

    // คำนวณสถิติรวม
    var stats = calculateStats(totalScores);

    // คำนวณ rank สำหรับแต่ละนักศึกษา
    var sortedScores = totalScores.slice().sort(function(a, b) { return b - a; });
    var ranks = calculateRanks(sortedScores);

    // กำหนดตำแหน่งคอลัมน์สถิติ - ใส่ต่อจาก last column ของ sheet ที่อัพโหลด
    var statStartCol = numCols + 1;

    // เพิ่ม headers สำหรับสถิติแต่ละคน (เพิ่มครั้งเดียว)
    if (!addedStatCols) {
      scoreSheet.getRange(1, statStartCol).setValue('Mean');
      scoreSheet.getRange(1, statStartCol + 1).setValue('SD');
      scoreSheet.getRange(1, statStartCol + 2).setValue('Min');
      scoreSheet.getRange(1, statStartCol + 3).setValue('Max');
      scoreSheet.getRange(1, statStartCol + 4).setValue('Rank');
      addedStatCols = true;
    }

    // เพิ่มสถิติสำหรับแต่ละนักศึกษา
    for (var r = 1; r < data.length; r++) {
      var studentScore = totalScores[r - 1];
      var studentRank = getRankForScore(studentScore, sortedScores, ranks);
      var rankDisplay = studentRank + '_' + totalScores.length; // ใช้ _ แทน / เพื่อป้องกัน Google Sheets แปลงเป็น date

      scoreSheet.getRange(r + 1, statStartCol).setValue(stats.mean.toFixed(2));
      scoreSheet.getRange(r + 1, statStartCol + 1).setValue(stats.sd.toFixed(2));
      scoreSheet.getRange(r + 1, statStartCol + 2).setValue(stats.min);
      scoreSheet.getRange(r + 1, statStartCol + 3).setValue(stats.max);
      scoreSheet.getRange(r + 1, statStartCol + 4).setValue(rankDisplay);
    }

    Logger.log('addStatisticsToSheet completed successfully');

  } catch (e) {
    Logger.log('addStatisticsToSheet ERROR: ' + e.toString());
    // ไม่ throw error เพื่อไม่ให้กระทบกับการอัปโหลดหลัก
  }
}

/**
 * จัดการการประกาศคะแนน
 * @param {string} instructorEmail อีเมลของอาจารย์
 * @param {string} action toggle หรือ delete
 * @param {string} scoreSheetName ชื่อชีตคะแนน
 * @returns {Object} ผลการจัดการ
 */
function manageScoreAnnouncement(instructorEmail, action, scoreSheetName) {
  try {
    var ss = SpreadsheetApp.getActiveSpreadsheet();
    var courseSheet = ss.getSheetByName('course_list');
    var courseData = courseSheet.getDataRange().getValues();

    // หาแถวของรายวิชานี้
    var targetRow = -1;
    for (var i = 1; i < courseData.length; i++) {
      var row = courseData[i];
      if (row[7] === scoreSheetName && row[5] === instructorEmail) {
        targetRow = i + 1;
        break;
      }
    }

    if (targetRow === -1) {
      return { error: 'ไม่พบรายวิชานี้หรือไม่มีสิทธิ์จัดการ' };
    }

    if (action === 'delete') {
      // ลบจาก course_list
      courseSheet.deleteRow(targetRow);

      // ลบชีตคะแนน
      var scoreSheet = ss.getSheetByName(scoreSheetName);
      if (scoreSheet) {
        ss.deleteSheet(scoreSheet);
      }

      return { success: true, message: 'ลบการประกาศคะแนนสำเร็จ' };

    } else if (action === 'toggle') {
      // Toggle สถานะการประกาศ - ใช้ column I (index 8) โดยตรง
      var currentStatus = courseSheet.getRange(targetRow, 9).getValue(); // Column I (1-indexed = 9)
      var newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      courseSheet.getRange(targetRow, 9).setValue(newStatus); // Column I

      return {
        success: true,
        message: 'เปลี่ยนสถานะการประกาศเป็น: ' + newStatus,
        newStatus: newStatus
      };
    }

    return { error: 'การดำเนินการไม่ถูกต้อง' };

  } catch (e) {
    Logger.log('manageScoreAnnouncement ERROR: ' + e);
    return { error: 'เกิดข้อผิดพลาด: ' + e.toString() };
  }
}

/**
 * ค้นหาคะแนนนักศึกษา (สำหรับอาจารย์)
 * @param {string} instructorEmail อีเมลของอาจารย์
 * @param {string} searchTerm คำค้นหา (ID, ชื่อ, นามสกุล)
 * @returns {Object} ผลการค้นหา
 */
function searchStudentScores(instructorEmail, searchTerm) {
  try {
    Logger.log('searchStudentScores called with email: ' + instructorEmail + ', searchTerm: ' + searchTerm);

    if (!searchTerm || searchTerm.trim() === '') {
      return { error: 'กรุณากรอกคำค้นหา' };
    }

    // ตรวจสอบว่าเป็นการค้นหา "example" หรือไม่
    if (searchTerm.toLowerCase().trim() === 'example') {
      Logger.log('Detected example search, calling generateExampleSearchResults');
      var exampleResult = generateExampleSearchResults();
      Logger.log('Example result generated: ' + JSON.stringify(exampleResult));
      return exampleResult;
    }

    var ss = SpreadsheetApp.getActiveSpreadsheet();
    var courseSheet = ss.getSheetByName('course_list');
    if (!courseSheet) {
      return { error: 'ไม่พบข้อมูลรายวิชา' };
    }

    // ดึงรายวิชาที่อาจารย์เป็นผู้ประกาศ
    var instructorCourses = getInstructorCourses(instructorEmail);
    Logger.log('Found instructor courses: ' + instructorCourses.length);

    if (!instructorCourses || instructorCourses.length === 0) {
      return {
        results: [],
        totalFound: 0,
        searchTerm: searchTerm,
        message: 'ไม่พบรายวิชาที่คุณเป็นผู้ประกาศ'
      };
    }

    var searchResults = [];
    var searchTermLower = searchTerm.toLowerCase().trim();

    // ค้นหาในแต่ละรายวิชาที่อาจารย์เป็นผู้ประกาศ
    for (var i = 0; i < instructorCourses.length; i++) {
      var course = instructorCourses[i];
      Logger.log('Processing course: ' + course.code + ', active: ' + course.isActive + ', sheet: ' + course.scoreSheetName);

      // ข้ามรายวิชาที่ไม่ active
      if (!course.isActive) {
        Logger.log('Skipping inactive course: ' + course.code);
        continue;
      }

      var scoreSheet = ss.getSheetByName(course.scoreSheetName);
      if (!scoreSheet) {
        Logger.log('Score sheet not found: ' + course.scoreSheetName);
        continue;
      }

      var data = scoreSheet.getDataRange().getValues();
      if (data.length < 2) {
        Logger.log('No data in score sheet: ' + course.scoreSheetName);
        continue;
      }

      var headers = data[0];
      Logger.log('Processing ' + (data.length - 1) + ' students in ' + course.scoreSheetName);

      // ค้นหานักศึกษาในชีตนี้
      for (var r = 1; r < data.length; r++) {
        var row = data[r];
        var studentId = String(row[0] || '').toLowerCase();
        var studentName = String(row[1] || '').toLowerCase();
        var studentEmail = String(row[2] || '').toLowerCase();

        // ตรวจสอบว่าตรงกับคำค้นหาหรือไม่
        if (studentId.includes(searchTermLower) ||
            studentName.includes(searchTermLower) ||
            studentEmail.includes(searchTermLower)) {

          Logger.log('Found matching student: ' + studentId + ' - ' + studentName);

          // สร้างข้อมูลคะแนนสำหรับนักศึกษาคนนี้
          // ตรวจสอบประเภท template สำหรับการค้นหา
          var templateType = detectTemplateType('', headers);
          var studentScores = buildStudentScoreData(row, headers, course, templateType);
          searchResults.push(studentScores);
        }
      }
    }

    Logger.log('Total search results found: ' + searchResults.length);

    // เรียงผลลัพธ์ตาม student ID
    searchResults.sort(function(a, b) {
      var idA = String(a.studentId || '');
      var idB = String(b.studentId || '');
      return idA.localeCompare(idB);
    });

    var result = {
      results: searchResults,
      totalFound: searchResults.length,
      searchTerm: searchTerm
    };

    Logger.log('Returning search result: ' + JSON.stringify(result));
    return result;

  } catch (e) {
    Logger.log('searchStudentScores ERROR: ' + e);
    return { error: 'เกิดข้อผิดพลาด: ' + e.toString() };
  }
}

/**
 * สร้างผลลัพธ์ตัวอย่างสำหรับการค้นหา "example"
 * @returns {Object} ผลลัพธ์ตัวอย่าง
 */
function generateExampleSearchResults() {
  try {
    Logger.log('Generating example search results');

    var exampleResults = [];

    // นักศึกษาตัวอย่าง - ใช้ชื่อแบบสุ่ม
    var randomNames = getRandomNames(1);
    var exampleStudent = {
      studentId: '6800001',
      studentName: randomNames[0],
      studentEmail: '<EMAIL>'
    };

    // รายวิชาที่ 1: วิชาที่มี 1 section
    var course1 = {
      studentId: exampleStudent.studentId,
      studentName: exampleStudent.studentName,
      studentEmail: exampleStudent.studentEmail,
      course: {
        code: 'DRD101',
        name: 'Perio 1',
        examType: 'Midterm',
        year: '2025',
        term: '1',
        announceDate: Utilities.formatDate(new Date(), Session.getScriptTimeZone(), 'yyyy-MM-dd HH:mm:ss')
      },
      sections: [
        {
          section: 'Written Exam',
          score: 85,
          fullScore: 100
        }
      ],
      stats: {
        mean: 78.5,
        sd: 8.2,
        min: 65,
        max: 95,
        rank: 8,
        total: 45
      }
    };

    // รายวิชาที่ 2: วิชาที่มี 2 sections
    var course2 = {
      studentId: exampleStudent.studentId,
      studentName: exampleStudent.studentName,
      studentEmail: exampleStudent.studentEmail,
      course: {
        code: 'DRD121',
        name: 'Oper 1',
        examType: 'Midterm',
        year: '2025',
        term: '1',
        announceDate: Utilities.formatDate(new Date(), Session.getScriptTimeZone(), 'yyyy-MM-dd HH:mm:ss')
      },
      sections: [
        {
          section: 'Lecture',
          score: 78,
          fullScore: 100
        },
        {
          section: 'Lab.',
          score: 92,
          fullScore: 100
        }
      ],
      stats: {
        mean: 162.3,
        sd: 6.7,
        min: 70,
        max: 198,
        rank: 12,
        total: 68
      }
    };

    // รายวิชาที่ 3: วิชาที่มี 3 sections
    var course3 = {
      studentId: exampleStudent.studentId,
      studentName: exampleStudent.studentName,
      studentEmail: exampleStudent.studentEmail,
      course: {
        code: 'DPR131',
        name: 'RPD 1',
        examType: 'Midterm',
        year: '2025',
        term: '1',
        announceDate: Utilities.formatDate(new Date(), Session.getScriptTimeZone(), 'yyyy-MM-dd HH:mm:ss')
      },
      sections: [
        {
          section: 'Multiple Choice',
          score: 88,
          fullScore: 100
        },
        {
          section: 'Essay',
          score: 75,
          fullScore: 100
        },
        {
          section: 'Case Study',
          score: 90,
          fullScore: 100
        }
      ],
      stats: {
        mean: 259.8,
        sd: 19.1,
        min: 158,
        max: 286,
        rank: 5,
        total: 52
      }
    };

    exampleResults.push(course1, course2, course3);

    return {
      results: exampleResults,
      totalFound: exampleResults.length,
      searchTerm: 'example',
      message: 'ผลลัพธ์ตัวอย่าง - แสดงคะแนนของนักศึกษาสมมติ'
    };

  } catch (e) {
    Logger.log('generateExampleSearchResults ERROR: ' + e);
    return { error: 'เกิดข้อผิดพลาดในการสร้างตัวอย่าง: ' + e.toString() };
  }
}

/**
 * สร้างข้อมูลคะแนนสำหรับนักศึกษา
 * @param {Array} studentRow แถวข้อมูลนักศึกษา
 * @param {Array} headers headers ของชีต
 * @param {Object} course ข้อมูลรายวิชา
 * @param {string} templateType ประเภท template ('score' หรือ 'custom')
 * @returns {Object} ข้อมูลคะแนนนักศึกษา
 */
function buildStudentScoreData(studentRow, headers, course, templateType) {
  var studentData = {
    studentId: String(studentRow[0] || ''),
    studentName: String(studentRow[1] || ''),
    studentEmail: String(studentRow[2] || ''),
    course: {
      code: course.code,
      name: course.name,
      examType: course.examType,
      year: course.year,
      term: course.term,
      announceDate: course.announceDate
    },
    sections: [],
    stats: {}
  };

  // หา sections ของคะแนน หรือ custom data columns
  var sectionIndexes = [];
  var customDataColumns = [];
  var isCustomData = (templateType === 'custom');

  if (isCustomData) {
    // เป็น custom data
    for (var i = 3; i < headers.length; i++) {
      customDataColumns.push({
        columnName: headers[i],
        columnIdx: i
      });
    }
  } else {
    // เป็น score data แบบปกติ
    for (var i = 3; i < headers.length - 1; i++) {
      var h1 = (headers[i] || '').toString().toLowerCase();
      var h2 = (headers[i+1] || '').toString().toLowerCase();

      if ((h1.includes('score') && h2.includes('full')) ||
          (h1.includes('คะแนน') && h2.includes('เต็ม'))) {

        // หาชื่อ section
        var sectionName = headers[i].replace(/score|คะแนน/gi, '').trim();
        if (!sectionName) {
          sectionName = 'Section ' + (sectionIndexes.length + 1);
        }

        sectionIndexes.push({
          section: sectionName,
          scoreIdx: i,
          fullScoreIdx: i + 1
        });
        i++; // skip next column (full score)
      }
    }
  }

  // สร้างข้อมูล sections หรือ custom data
  if (isCustomData) {
    // สำหรับ custom data
    for (var j = 0; j < customDataColumns.length; j++) {
      var col = customDataColumns[j];
      studentData.sections.push({
        section: String(col.columnName || ''),
        data: String(studentRow[col.columnIdx] || ''),
        isCustomData: true
      });
    }
  } else {
    // สำหรับ score data แบบปกติ
    for (var j = 0; j < sectionIndexes.length; j++) {
      var sec = sectionIndexes[j];
      studentData.sections.push({
        section: String(sec.section || ''),
        score: Number(studentRow[sec.scoreIdx]) || 0,
        fullScore: Number(studentRow[sec.fullScoreIdx]) || 0
      });
    }
  }

  // อ่านสถิติจาก headers ที่มีอยู่ (เฉพาะสำหรับ score data, ไม่ใช่ custom data)
  if (!isCustomData) {
    for (var k = 0; k < headers.length; k++) {
      var header = (headers[k] || '').toString().toLowerCase();
      if (header === 'mean') {
        studentData.stats.mean = parseFloat(studentRow[k]) || 0;
      } else if (header === 'sd') {
        studentData.stats.sd = parseFloat(studentRow[k]) || 0;
      } else if (header === 'min') {
        studentData.stats.min = parseFloat(studentRow[k]) || 0;
      } else if (header === 'max') {
        studentData.stats.max = parseFloat(studentRow[k]) || 0;
      } else if (header === 'rank') {
        var rankValue = studentRow[k] || '';
        if (typeof rankValue === 'string') {
          if (rankValue.includes('/')) {
            var rankParts = rankValue.split('/');
            studentData.stats.rank = parseInt(rankParts[0]) || 1;
            studentData.stats.total = parseInt(rankParts[1]) || 1;
          } else if (rankValue.includes('_')) {
            var rankParts = rankValue.split('_');
            studentData.stats.rank = parseInt(rankParts[0]) || 1;
            studentData.stats.total = parseInt(rankParts[1]) || 1;
          }
        }
      }
    }
  }

  return studentData;
}

/**
 * ดึงข้อมูลตารางคะแนนสำหรับแสดงใน modal
 * @param {string} scoreSheetName ชื่อชีตคะแนน
 * @returns {Object} ข้อมูลตารางคะแนน
 */
function getScoreTableData(scoreSheetName) {
  try {
    var ss = SpreadsheetApp.getActiveSpreadsheet();
    var scoreSheet = ss.getSheetByName(scoreSheetName);

    if (!scoreSheet) {
      return { error: 'ไม่พบชีตคะแนน' };
    }

    var data = scoreSheet.getDataRange().getValues();

    if (data.length === 0) {
      return { error: 'ไม่มีข้อมูลในชีตคะแนน' };
    }

    var headers = data[0];
    var scores = data.slice(1); // ข้อมูลนักศึกษา (ไม่รวม header)

    return {
      success: true,
      headers: headers,
      scores: scores
    };

  } catch (e) {
    Logger.log('getScoreTableData ERROR: ' + e);
    return { error: 'เกิดข้อผิดพลาด: ' + e.toString() };
  }
}