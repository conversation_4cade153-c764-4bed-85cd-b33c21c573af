              <div class="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
                <p class="text-xs text-blue-700">
                  <i class="fas fa-circle mr-2" style="font-size: 4px; vertical-align: middle;"></i>
                  ท่านสามารถค้นหาคะแนนของนักศึกษาในรายวิชาที่ท่านเป็นผู้ประกาศเท่านั้น
                </p>
                <p class="text-xs text-blue-700">
                  <i class="fas fa-circle mr-2" style="font-size: 4px; vertical-align: middle;"></i>
                  ทดสอบการประกาศคะแนนโดยค้นหา ID หรือชื่อนักศึกษา หรือลองค้นหาคำว่า "example" เพื่อดูตัวอย่างผลคะแนน
                </p>
              </div>



                 <div class="p-3 bg-blue-50 border border-blue-200 rounded">
                    <ul class="text-xs text-blue-700 list-disc list-inside space-y-1">
                      <li>คอลัมน์คะแนนต้องเรียงเป็น "คะแนนที่ได้" ตามด้วย "คะแนนเต็ม"</li>
                      <li>หากแยกคะแนนเป็นพาร์ท เช่น Part A, Part B ฯลฯ ให้เรียงเป็น Part A คะแนนที่ได้ → Part A คะแนนเต็ม → Part B คะแนนที่ได้ → Part B คะแนนเต็ม ไปเรื่อยๆ ปิดท้ายด้วยคะแนนรวมและคะแนนเต็มรวมในสองคอลัมน์สุดท้าย</li>
                    </ul>
                  </div>



                  <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
                <div class="text-xs text-yellow-700">
                  <p class="mb-2">
                    <i class="fas fa-circle mr-2" style="font-size: 4px; vertical-align: middle;"></i>
                    คะแนนจะแสดงผล 30 วัน หลังจากนั้นจะถูกจะถูกลบโดยอัตโนมัติ
                  </p>
                  <p>
                    <i class="fas fa-circle mr-2" style="font-size: 4px; vertical-align: middle;"></i>
                    กดปุ่มไลน์ เพื่อ Copy ลิงค์ประกาศคะแนน และส่งให้นักศึกษาได้เลย
                  </p>
                </div>


              <div class="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4"> 
                <p class="text-xs text-blue-700"> 
                  <i class="fas fa-circle mr-2" style="font-size: 4px; vertical-align: middle;"></i> 
                  ท่านสามารถค้นหาคะแนนของนักศึกษาในรายวิชาที่ท่านเป็นผู้ประกาศเท่านั้น 
                </p> 
                <p class="text-xs text-blue-700"> 
                  <i class="fas fa-circle mr-2" style="font-size: 4px; vertical-align: middle;"></i> 
                  ทดสอบการประกาศคะแนนโดยค้นหา ID หรือชื่อนักศึกษา หรือลองค้นหาคำว่า "example" เพื่อดูตัวอย่างผลคะแนน 
                </p> 
              </div> 
 
 
 
                 <div class="p-3 bg-blue-50 border border-blue-200 rounded"> 
                    <ul class="text-xs text-blue-700 list-disc list-inside space-y-1"> 
                      <li>คอลัมน์คะแนนต้องเรียงเป็น "คะแนนที่ได้" ตามด้วย "คะแนนเต็ม"</li> 
                      <li>หากแยกคะแนนเป็นพาร์ท เช่น Part A, Part B ฯลฯ ให้เรียงเป็น Part A คะแนนที่ได้ → Part A คะแนนเต็ม → Part B คะแนนที่ได้ → Part B คะแนนเต็ม ไปเรื่อยๆ ปิดท้ายด้วยคะแนนรวมและคะแนนเต็มรวมในสองคอลัมน์สุดท้าย</li> 
                    </ul> 
                  </div> 
 
 
 
                  <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4"> 
                <div class="text-xs text-yellow-700"> 
                  <p class="mb-2"> 
                    <i class="fas fa-circle mr-2" style="font-size: 4px; vertical-align: middle;"></i> 
                    คะแนนจะแสดงผล 30 วัน หลังจากนั้นจะถูกจะถูกลบโดยอัตโนมัติ 
                  </p> 
                  <p> 
                    <i class="fas fa-circle mr-2" style="font-size: 4px; vertical-align: middle;"></i> 
                    กดปุ่มไลน์ เพื่อ Copy ลิงค์ประกาศคะแนน และส่งให้นักศึกษาได้เลย 
                  </p> 
                </div>

@y:\Nextcloud\Coding\ScareScore 20250728/index.html 3 ส่วนนี้จัดรูปแบบให้เหมิอนกัน ใช้โค้ดแบบ li /li

แต่คงสีเดิมครับ