/**
 * Archive System for ScareScore
 * ระบบ archive อัตโนมัติสำหรับย้ายข้อมูลคะแนนที่เก่ากว่า 30 วัน
 */

// Constants
const MAIN_SHEET_ID = '1Z8w5GUIBhTfZqBthxccQxFD9k0DXv40kiSg_tNxzdUc';
const ARCHIVE_SHEET_ID = '1iN2pNyXKvzoXb-lxAiR2aHyZ-LQbwDugZIyiC51CEr0';
const ARCHIVE_SHEET_NAME = 'archiveLog';
const DAYS_BEFORE_ARCHIVE = 30;

/**
 * Main function: ตรวจสอบและย้ายข้อมูลที่เก่ากว่า 30 วัน
 */
function archiveOldCourses() {
  try {
    Logger.log('=== Starting Archive Process ===');
    
    // เปิด spreadsheet หลัก
    var mainSS = SpreadsheetApp.openById(MAIN_SHEET_ID);
    var courseSheet = mainSS.getSheetByName('course_list');
    
    if (!courseSheet) {
      Logger.log('ERROR: course_list sheet not found');
      return { error: 'ไม่พบชีต course_list' };
    }
    
    // ดึงข้อมูลทั้งหมดจาก course_list
    var data = courseSheet.getDataRange().getValues();
    if (data.length <= 1) {
      Logger.log('No courses to archive');
      return { message: 'ไม่มีข้อมูลที่ต้องย้าย' };
    }
    
    var headers = data[0];
    var courses = data.slice(1);
    
    // หาคอลัมน์ที่เกี่ยวข้อง
    var dateIndex = headers.indexOf('วันที่ประกาศ');
    var sheetNameIndex = headers.indexOf('ชื่อชีตคะแนน');
    
    if (dateIndex === -1 || sheetNameIndex === -1) {
      Logger.log('ERROR: Required columns not found');
      return { error: 'ไม่พบคอลัมน์ที่จำเป็น' };
    }
    
    // หาข้อมูลที่ต้องย้าย (เก่ากว่า 30 วัน)
    var today = new Date();
    var coursesToArchive = [];
    var rowsToDelete = [];
    
    for (var i = 0; i < courses.length; i++) {
      var course = courses[i];
      var announceDate = new Date(course[dateIndex]);
      var daysDiff = Math.floor((today - announceDate) / (1000 * 60 * 60 * 24));
      
      if (daysDiff >= DAYS_BEFORE_ARCHIVE) {
        coursesToArchive.push({
          data: course,
          rowIndex: i + 2, // +2 เพราะ array เริ่มจาก 0 และมี header
          scoreSheetName: course[sheetNameIndex]
        });
        rowsToDelete.push(i + 2);
      }
    }
    
    if (coursesToArchive.length === 0) {
      Logger.log('No courses older than ' + DAYS_BEFORE_ARCHIVE + ' days found');
      return { message: 'ไม่มีข้อมูลที่เก่ากว่า ' + DAYS_BEFORE_ARCHIVE + ' วัน' };
    }
    
    Logger.log('Found ' + coursesToArchive.length + ' courses to archive');
    
    // ย้ายข้อมูลไป archive
    var archiveResult = moveCoursesToArchive(coursesToArchive, headers);
    if (archiveResult.error) {
      return archiveResult;
    }
    
    // ย้ายชีตคะแนน
    var moveSheetResult = moveScoreSheetsToArchive(coursesToArchive, mainSS);
    if (moveSheetResult.error) {
      Logger.log('WARNING: ' + moveSheetResult.error);
    }
    
    // ลบข้อมูลจาก course_list (เรียงจากมากไปน้อยเพื่อไม่ให้ index เปลี่ยน)
    rowsToDelete.sort(function(a, b) { return b - a; });
    for (var j = 0; j < rowsToDelete.length; j++) {
      courseSheet.deleteRow(rowsToDelete[j]);
    }
    
    Logger.log('=== Archive Process Completed ===');
    return {
      success: true,
      message: 'ย้ายข้อมูล ' + coursesToArchive.length + ' รายการเข้า archive สำเร็จ',
      archivedCount: coursesToArchive.length
    };
    
  } catch (e) {
    Logger.log('ERROR in archiveOldCourses: ' + e.toString());
    return { error: 'เกิดข้อผิดพลาด: ' + e.toString() };
  }
}

/**
 * ตรวจสอบและสร้าง archive sheet ถ้ายังไม่มี
 */
function ensureArchiveSheetExists() {
  try {
    var archiveSS = SpreadsheetApp.openById(ARCHIVE_SHEET_ID);
    var archiveSheet = archiveSS.getSheetByName(ARCHIVE_SHEET_NAME);

    if (!archiveSheet) {
      Logger.log('Creating new archive sheet: ' + ARCHIVE_SHEET_NAME);
      archiveSheet = archiveSS.insertSheet(ARCHIVE_SHEET_NAME);

      // สร้าง headers ที่ถูกต้องตามโครงสร้าง
      var archiveHeaders = [
        'รหัสวิชา',
        'ชื่อวิชา',
        'ประเภทสอบ',
        'ปีการศึกษา',
        'ภาคการศึกษา',
        'อาจารย์ผู้ประกาศ',
        'วันที่ประกาศ',
        'ชื่อชีตคะแนน',
        'สถานะ',
        'วันที่ย้ายเข้า archive'
      ];

      archiveSheet.clear();
      archiveSheet.appendRow(archiveHeaders);

      // จัดรูปแบบ header
      var headerRange = archiveSheet.getRange(1, 1, 1, archiveHeaders.length);
      headerRange.setFontWeight('bold');
      headerRange.setBackground('#f0f0f0');

      Logger.log('Archive sheet created successfully with headers');
    } else {
      Logger.log('Archive sheet already exists');
    }

    return { success: true, sheet: archiveSheet };

  } catch (e) {
    Logger.log('ERROR in ensureArchiveSheetExists: ' + e.toString());
    return { error: 'ไม่สามารถสร้าง archive sheet ได้: ' + e.toString() };
  }
}

/**
 * ย้ายข้อมูล course ไป archive sheet
 */
function moveCoursesToArchive(coursesToArchive, headers) {
  try {
    // ตรวจสอบและสร้าง archive sheet
    var archiveResult = ensureArchiveSheetExists();
    if (archiveResult.error) {
      return archiveResult;
    }

    var archiveSheet = archiveResult.sheet;

    // เพิ่มข้อมูลลงใน archive sheet
    var today = new Date();
    for (var i = 0; i < coursesToArchive.length; i++) {
      var courseData = coursesToArchive[i].data.concat([today]);
      archiveSheet.appendRow(courseData);
    }

    Logger.log('Successfully moved ' + coursesToArchive.length + ' courses to archive');
    return { success: true };

  } catch (e) {
    Logger.log('ERROR in moveCoursesToArchive: ' + e.toString());
    return { error: 'ไม่สามารถย้ายข้อมูลไป archive ได้: ' + e.toString() };
  }
}

/**
 * ย้ายชีตคะแนนไป archive spreadsheet
 */
function moveScoreSheetsToArchive(coursesToArchive, mainSS) {
  try {
    var archiveSS = SpreadsheetApp.openById(ARCHIVE_SHEET_ID);
    var movedCount = 0;
    var errors = [];
    
    for (var i = 0; i < coursesToArchive.length; i++) {
      var scoreSheetName = coursesToArchive[i].scoreSheetName;
      
      if (!scoreSheetName) {
        continue;
      }
      
      try {
        // หาชีตคะแนนใน main spreadsheet
        var scoreSheet = mainSS.getSheetByName(scoreSheetName);
        if (!scoreSheet) {
          Logger.log('WARNING: Score sheet "' + scoreSheetName + '" not found');
          errors.push('ไม่พบชีตคะแนน: ' + scoreSheetName);
          continue;
        }
        
        // คัดลอกชีตไป archive spreadsheet
        var copiedSheet = scoreSheet.copyTo(archiveSS);
        copiedSheet.setName(scoreSheetName + '_archived_' + Utilities.formatDate(new Date(), 'GMT+7', 'yyyyMMdd'));
        
        // ลบชีตจาก main spreadsheet
        mainSS.deleteSheet(scoreSheet);
        
        movedCount++;
        Logger.log('Successfully moved score sheet: ' + scoreSheetName);
        
      } catch (e) {
        Logger.log('ERROR moving score sheet "' + scoreSheetName + '": ' + e.toString());
        errors.push('ไม่สามารถย้ายชีตคะแนน "' + scoreSheetName + '": ' + e.toString());
      }
    }
    
    if (errors.length > 0) {
      return { 
        success: true, 
        movedCount: movedCount,
        warning: 'ย้ายชีตคะแนนได้ ' + movedCount + ' ชีต, มีปัญหา: ' + errors.join(', ')
      };
    }
    
    return { 
      success: true, 
      movedCount: movedCount,
      message: 'ย้ายชีตคะแนนทั้งหมด ' + movedCount + ' ชีตสำเร็จ'
    };
    
  } catch (e) {
    Logger.log('ERROR in moveScoreSheetsToArchive: ' + e.toString());
    return { error: 'ไม่สามารถย้ายชีตคะแนนได้: ' + e.toString() };
  }
}

/**
 * ตั้งค่า Time-based trigger สำหรับรัน archive อัตโนมัติ
 */
function setupArchiveTrigger() {
  try {
    // ลบ trigger เก่าถ้ามี
    var triggers = ScriptApp.getProjectTriggers();
    for (var i = 0; i < triggers.length; i++) {
      if (triggers[i].getHandlerFunction() === 'archiveOldCourses') {
        ScriptApp.deleteTrigger(triggers[i]);
      }
    }
    
    // สร้าง trigger ใหม่ - รันทุกวันเวลา 02:00
    ScriptApp.newTrigger('archiveOldCourses')
      .timeBased()
      .everyDays(1)
      .atHour(2)
      .create();
    
    Logger.log('Archive trigger setup successfully');
    return { success: true, message: 'ตั้งค่า trigger สำเร็จ - จะรันทุกวันเวลา 02:00' };
    
  } catch (e) {
    Logger.log('ERROR in setupArchiveTrigger: ' + e.toString());
    return { error: 'ไม่สามารถตั้งค่า trigger ได้: ' + e.toString() };
  }
}

/**
 * ลบ archive trigger
 */
function removeArchiveTrigger() {
  try {
    var triggers = ScriptApp.getProjectTriggers();
    var deletedCount = 0;
    
    for (var i = 0; i < triggers.length; i++) {
      if (triggers[i].getHandlerFunction() === 'archiveOldCourses') {
        ScriptApp.deleteTrigger(triggers[i]);
        deletedCount++;
      }
    }
    
    Logger.log('Removed ' + deletedCount + ' archive triggers');
    return { success: true, message: 'ลบ trigger สำเร็จ (' + deletedCount + ' triggers)' };
    
  } catch (e) {
    Logger.log('ERROR in removeArchiveTrigger: ' + e.toString());
    return { error: 'ไม่สามารถลบ trigger ได้: ' + e.toString() };
  }
}

/**
 * ตรวจสอบสถานะ trigger
 */
function checkArchiveTriggerStatus() {
  try {
    var triggers = ScriptApp.getProjectTriggers();
    var archiveTriggers = [];
    
    for (var i = 0; i < triggers.length; i++) {
      if (triggers[i].getHandlerFunction() === 'archiveOldCourses') {
        archiveTriggers.push({
          id: triggers[i].getUniqueId(),
          eventType: triggers[i].getEventType(),
          source: triggers[i].getTriggerSource()
        });
      }
    }
    
    return {
      success: true,
      triggerCount: archiveTriggers.length,
      triggers: archiveTriggers,
      message: 'พบ archive trigger จำนวน ' + archiveTriggers.length + ' triggers'
    };
    
  } catch (e) {
    Logger.log('ERROR in checkArchiveTriggerStatus: ' + e.toString());
    return { error: 'ไม่สามารถตรวจสอบสถานะ trigger ได้: ' + e.toString() };
  }
}

/**
 * ทดสอบระบบ archive (สำหรับ manual testing)
 */
function testArchiveSystem() {
  Logger.log('=== Testing Archive System ===');
  
  // ทดสอบการตรวจสอบ trigger
  var triggerStatus = checkArchiveTriggerStatus();
  Logger.log('Trigger Status: ' + JSON.stringify(triggerStatus));
  
  // ทดสอบการหาข้อมูลที่ต้องย้าย (แต่ไม่ย้ายจริง)
  try {
    var mainSS = SpreadsheetApp.openById(MAIN_SHEET_ID);
    var courseSheet = mainSS.getSheetByName('course_list');
    
    if (!courseSheet) {
      Logger.log('ERROR: course_list sheet not found');
      return;
    }
    
    var data = courseSheet.getDataRange().getValues();
    if (data.length <= 1) {
      Logger.log('No courses found');
      return;
    }
    
    var headers = data[0];
    var courses = data.slice(1);
    var dateIndex = headers.indexOf('วันที่ประกาศ');
    
    if (dateIndex === -1) {
      Logger.log('ERROR: วันที่ประกาศ column not found');
      return;
    }
    
    var today = new Date();
    var oldCourses = 0;
    
    for (var i = 0; i < courses.length; i++) {
      var announceDate = new Date(courses[i][dateIndex]);
      var daysDiff = Math.floor((today - announceDate) / (1000 * 60 * 60 * 24));
      
      if (daysDiff >= DAYS_BEFORE_ARCHIVE) {
        oldCourses++;
        Logger.log('Course to archive: ' + courses[i][0] + ' (' + daysDiff + ' days old)');
      }
    }
    
    Logger.log('Total courses that would be archived: ' + oldCourses);
    
  } catch (e) {
    Logger.log('ERROR in testArchiveSystem: ' + e.toString());
  }
  
  Logger.log('=== Test Completed ===');
}
