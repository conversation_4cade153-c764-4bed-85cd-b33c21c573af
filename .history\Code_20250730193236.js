function doGet(e) {
  var template = HtmlService.createTemplateFromFile('index');
  return template
    .evaluate()
    .setFaviconUrl("https://cdn-icons-png.flaticon.com/512/13726/13726350.png")
    .setTitle('ScareScore')
    .addMetaTag('viewport', 'width=device-width, initial-scale=1');
}

/**
 * Web API: ดึงคะแนนจากชีทคะแนนรายวิชา
 * @param {string} sheetName
 * @return {Array} array of score objects
 */
function doGetScores(sheetName) {
  return getSampleCourseScores(sheetName);
}

/**
 * Web API: ดึงรายวิชาทั้งหมดของนักศึกษา (ไม่ต้องรับ email)
 */
function doGetStudentCourses() {
  try {
    var email = Session.getActiveUser().getEmail();
    logAccess(email.split('@')[0], email, 'เรียกดูรายวิชา', null);
    
    var result = getStudentCourses();
    Logger.log('doGetStudentCourses result: ' + JSON.stringify(result));
    if (!result) return [];
    return result;
  } catch (e) {
    Logger.log('doGetStudentCourses ERROR: ' + e);
    return [];
  }
}

/**
 * Web API: ดึงคะแนนและ stat ของนักศึกษาในรายวิชา (ไม่ต้องรับ email)
 */
function doGetStudentCourseScores(sheetName) {
  try {
    var email = Session.getActiveUser().getEmail();
    logAccess(email.split('@')[0], email, 'เรียกดูคะแนน', sheetName);
    
    var result = getStudentCourseScores(sheetName);
    
    // เพิ่มข้อมูล allScores สำหรับการคำนวณ rank
    if (result && result.stats) {
      var ss = SpreadsheetApp.getActiveSpreadsheet();
      var sheet = ss.getSheetByName(sheetName);
      if (sheet) {
        var data = sheet.getDataRange().getValues();
        var allScores = [];
        for (var r = 1; r < data.length; r++) {
          var v = data[r][data[0].length - 2]; // col รองสุดท้าย = คะแนนที่ได้
          if (typeof v === 'number' || (!isNaN(parseFloat(v)) && isFinite(v))) {
            allScores.push(Number(v));
          }
        }
        result.allScores = allScores;
      }
    }
    
    return result;
  } catch (e) {
    Logger.log('doGetStudentCourseScores ERROR: ' + e);
    return { scores: [], stats: {} };
  }
}

/**
 * Web API: ตรวจสอบว่าผู้ใช้เป็น instructor หรือไม่
 */
function doCheckInstructorStatus() {
  try {
    var email = Session.getActiveUser().getEmail();
    if (!email) {
      return { isInstructor: false, isAdmin: false };
    }
    
    var instructorStatus = isInstructor(email);
    var adminStatus = isAdmin(email);
    
    logAccess(email.split('@')[0], email, 'ตรวจสอบสิทธิ์', 'instructor: ' + instructorStatus + ', admin: ' + adminStatus);
    
    return {
      isInstructor: instructorStatus,
      isAdmin: adminStatus,
      email: email
    };
  } catch (e) {
    Logger.log('doCheckInstructorStatus ERROR: ' + e);
    return { isInstructor: false, isAdmin: false, error: e.toString() };
  }
}

/**
 * Web API: ดึงข้อมูล instructor ทั้งหมด (สำหรับ admin เท่านั้น)
 */
function doGetAllInstructors() {
  try {
    var email = Session.getActiveUser().getEmail();
    if (!email || !isAdmin(email)) {
      return { error: 'ไม่มีสิทธิ์เข้าถึง' };
    }
    
    logAccess(email.split('@')[0], email, 'ดูรายชื่อ instructor', null);
    
    return getAllInstructors();
  } catch (e) {
    Logger.log('doGetAllInstructors ERROR: ' + e);
    return { error: e.toString() };
  }
}



/**
 * Web API: ค้นหาคะแนนนักศึกษา (สำหรับอาจารย์)
 * @param {string} searchTerm คำค้นหา (ID, ชื่อ, นามสกุล)
 */
function doSearchStudentScores(searchTerm) {
  try {
    Logger.log('doSearchStudentScores called with searchTerm: ' + searchTerm);
    var email = Session.getActiveUser().getEmail();
    Logger.log('User email: ' + email);

    if (!email || !isInstructor(email)) {
      Logger.log('Access denied - not instructor');
      return { error: 'ไม่มีสิทธิ์เข้าถึง' };
    }

    logAccess(email.split('@')[0], email, 'ค้นหาคะแนนนักศึกษา', searchTerm);

    var result = searchStudentScores(email, searchTerm);
    Logger.log('Search result from searchStudentScores: ' + JSON.stringify(result));
    return result;
  } catch (e) {
    Logger.log('doSearchStudentScores ERROR: ' + e);
    return { error: e.toString() };
  }
}

/**
 * Web API: ดึงรายวิชาที่อาจารย์เป็นผู้ประกาศ
 */
function doGetInstructorCourses() {
  try {
    var email = Session.getActiveUser().getEmail();
    if (!email || !isInstructor(email)) {
      return { error: 'ไม่มีสิทธิ์เข้าถึง' };
    }
    
    logAccess(email.split('@')[0], email, 'ดูรายวิชาที่ประกาศ', null);
    
    return getInstructorCourses(email);
  } catch (e) {
    Logger.log('doGetInstructorCourses ERROR: ' + e);
    return { error: e.toString() };
  }
}

/**
 * Web API: ดาวน์โหลด template สำหรับกรอกคะแนน
 */
function doDownloadTemplate() {
  try {
    var email = Session.getActiveUser().getEmail();
    if (!email || !isInstructor(email)) {
      return { error: 'ไม่มีสิทธิ์เข้าถึง' };
    }
    
    logAccess(email.split('@')[0], email, 'ดาวน์โหลด template', null);
    
    return generateScoreTemplate();
  } catch (e) {
    Logger.log('doDownloadTemplate ERROR: ' + e);
    return { error: e.toString() };
  }
}

/**
 * Web API: ดาวน์โหลด CSV template สำหรับกรอกคะแนน
 */
function doDownloadCSVTemplate() {
  try {
    var email = Session.getActiveUser().getEmail();
    if (!email || !isInstructor(email)) {
      return { error: 'ไม่มีสิทธิ์เข้าถึง' };
    }
    
    logAccess(email.split('@')[0], email, 'ดาวน์โหลด CSV template', null);
    
    return generateCSVTemplate();
  } catch (e) {
    Logger.log('doDownloadCSVTemplate ERROR: ' + e);
    return { error: e.toString() };
  }
}

/**
 * Test function สำหรับทดสอบการประมวลผลคะแนน
 */
function testScoreProcessing() {
  try {
    // สร้างข้อมูลทดสอบ
    var testData = [
      ['student ID', 'student name', 'student email', 'score', 'full score'],
      ['7000001', 'สมชาย ใจดี', '<EMAIL>', 78, 100],
      ['7000002', 'สมหญิง เก่งมาก', '<EMAIL>', 85, 100],
      ['7000003', 'สมปอง สู้ๆ', '<EMAIL>', 92, 100]
    ];
    
    // ทดสอบการทำความสะอาดข้อมูล
    var cleanedResult = cleanScoreData(testData);
    Logger.log('Test cleanScoreData result: ' + JSON.stringify(cleanedResult));
    
    // ทดสอบการสร้างชีตคะแนน
    var ss = SpreadsheetApp.getActiveSpreadsheet();
    var testSheetName = 'TEST_SCORE_' + new Date().getTime();
    var testSheet = ss.insertSheet(testSheetName);
    
    // เขียนข้อมูลทดสอบ
    var headers = testData[0];
    testSheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    testSheet.getRange(2, 1, testData.length - 1, headers.length).setValues(testData.slice(1));
    
    // ทดสอบการเพิ่มสถิติ
    addStatisticsToSheet(testSheet, headers);
    
    // ลบชีตทดสอบ
    ss.deleteSheet(testSheet);
    
    return {
      success: true,
      message: 'การทดสอบสำเร็จ',
      cleanedData: cleanedResult
    };
    
  } catch (e) {
    Logger.log('testScoreProcessing ERROR: ' + e);
    return { error: e.toString() };
  }
}

/**
 * Web API: อัปโหลดไฟล์คะแนน
 * @param {Object} uploadData ข้อมูลการอัปโหลด
 */
function doUploadScores(uploadData) {
  try {
    var email = Session.getActiveUser().getEmail();
    if (!email || !isInstructor(email)) {
      return { error: 'ไม่มีสิทธิ์เข้าถึง' };
    }
    
    logAccess(email.split('@')[0], email, 'อัปโหลดคะแนน', uploadData.courseCode);
    
    return processScoreUpload(email, uploadData);
  } catch (e) {
    Logger.log('doUploadScores ERROR: ' + e);
    return { error: e.toString() };
  }
}

/**
 * Web API: จัดการการประกาศคะแนน (toggle, delete)
 * @param {string} action toggle หรือ delete
 * @param {string} scoreSheetName ชื่อชีตคะแนน
 */
function doManageScoreAnnouncement(action, scoreSheetName) {
  try {
    var email = Session.getActiveUser().getEmail();
    if (!email || !isInstructor(email)) {
      return { error: 'ไม่มีสิทธิ์เข้าถึง' };
    }
    
    logAccess(email.split('@')[0], email, 'จัดการประกาศคะแนน', action + ' ' + scoreSheetName);
    
    return manageScoreAnnouncement(email, action, scoreSheetName);
  } catch (e) {
    Logger.log('doManageScoreAnnouncement ERROR: ' + e);
    return { error: e.toString() };
  }
}

/**
 * Test function สำหรับทดสอบ instructor dashboard
 */
function testInstructorDashboard() {
  try {
    // ทดสอบการสร้าง template
    var template = generateScoreTemplate();
    Logger.log('Template test: ' + JSON.stringify(template));

    // ทดสอบการตรวจสอบสิทธิ์
    var email = Session.getActiveUser().getEmail();
    var instructorStatus = isInstructor(email);
    var adminStatus = isAdmin(email);
    Logger.log('Instructor status: ' + instructorStatus + ', Admin status: ' + adminStatus);

    // ทดสอบการสร้างชื่อชีตที่ไม่ซ้ำ
    var uniqueName = generateUniqueSheetName('TEST_COURSE');
    Logger.log('Unique sheet name: ' + uniqueName);

    return {
      success: true,
      template: template,
      instructorStatus: instructorStatus,
      adminStatus: adminStatus,
      uniqueName: uniqueName
    };
  } catch (e) {
    Logger.log('testInstructorDashboard ERROR: ' + e);
    return { error: e.toString() };
  }
}

/**
 * Test function สำหรับทดสอบระบบค้นหา
 */
function testSearchFunction() {
  try {
    var email = Session.getActiveUser().getEmail();

    // ทดสอบการค้นหาด้วย ID
    var searchResult1 = searchStudentScores(email, '7000001');
    Logger.log('Search by ID result: ' + JSON.stringify(searchResult1));

    // ทดสอบการค้นหาด้วยชื่อ
    var searchResult2 = searchStudentScores(email, 'สมชาย');
    Logger.log('Search by name result: ' + JSON.stringify(searchResult2));

    // ทดสอบการค้นหาด้วยอีเมล
    var searchResult3 = searchStudentScores(email, 'somchai');
    Logger.log('Search by email result: ' + JSON.stringify(searchResult3));

    // ทดสอบการค้นหาตัวอย่าง
    var searchResult4 = searchStudentScores(email, 'example');
    Logger.log('Search example result: ' + JSON.stringify(searchResult4));

    // ทดสอบ generateExampleSearchResults โดยตรง
    var directExample = generateExampleSearchResults();
    Logger.log('Direct example result: ' + JSON.stringify(directExample));

    return {
      success: true,
      searchById: searchResult1,
      searchByName: searchResult2,
      searchByEmail: searchResult3,
      searchExample: searchResult4
    };
  } catch (e) {
    Logger.log('testSearchFunction ERROR: ' + e);
    return { error: e.toString() };
  }
}

/**
 * Web API: ทดสอบ example search โดยตรง (ไม่ตรวจสอบสิทธิ์)
 */
function doTestExampleSearch() {
  try {
    Logger.log('doTestExampleSearch called');
    var result = generateExampleSearchResults();
    Logger.log('Example result: ' + JSON.stringify(result));
    return result;
  } catch (e) {
    Logger.log('doTestExampleSearch ERROR: ' + e);
    return { error: e.toString() };
  }
}

/**
 * Test function สำหรับทดสอบ example search โดยตรง
 */
function testExampleSearch() {
  try {
    Logger.log('=== Testing Example Search ===');

    // ทดสอบ generateExampleSearchResults โดยตรง
    var directResult = generateExampleSearchResults();
    Logger.log('Direct generateExampleSearchResults result:');
    Logger.log(JSON.stringify(directResult, null, 2));

    // ทดสอบผ่าน searchStudentScores
    var email = Session.getActiveUser().getEmail();
    var searchResult = searchStudentScores(email, 'example');
    Logger.log('searchStudentScores with "example" result:');
    Logger.log(JSON.stringify(searchResult, null, 2));

    // ทดสอบผ่าน doSearchStudentScores
    var webApiResult = doSearchStudentScores('example');
    Logger.log('doSearchStudentScores with "example" result:');
    Logger.log(JSON.stringify(webApiResult, null, 2));

    return {
      success: true,
      directResult: directResult,
      searchResult: searchResult,
      webApiResult: webApiResult
    };

  } catch (e) {
    Logger.log('testExampleSearch ERROR: ' + e);
    return { error: e.toString() };
  }
}