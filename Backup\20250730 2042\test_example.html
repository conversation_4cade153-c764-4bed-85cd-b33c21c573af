<!DOCTYPE html>
<html>
<head>
    <title>Test Example Search</title>
</head>
<body>
    <h1>Test Example Search</h1>
    <button onclick="testExample()">Test Example Search (with auth)</button>
    <button onclick="testExampleDirect()">Test Example Direct (no auth)</button>
    <div id="result"></div>

    <script>
        function testExample() {
            console.log('Testing example search...');
            document.getElementById('result').innerHTML = 'Testing...';

            google.script.run
                .withSuccessHandler(function(result) {
                    console.log('Success:', result);
                    document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
                })
                .withFailureHandler(function(error) {
                    console.log('Error:', error);
                    document.getElementById('result').innerHTML = '<div style="color: red;">Error: ' + error.message + '</div>';
                })
                .doSearchStudentScores('example');
        }

        function testExampleDirect() {
            console.log('Testing example search direct...');
            document.getElementById('result').innerHTML = 'Testing direct...';

            google.script.run
                .withSuccessHandler(function(result) {
                    console.log('Direct Success:', result);
                    document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
                })
                .withFailureHandler(function(error) {
                    console.log('Direct Error:', error);
                    document.getElementById('result').innerHTML = '<div style="color: red;">Direct Error: ' + error.message + '</div>';
                })
                .doTestExampleSearch();
        }
    </script>
</body>
</html>
